# CS2 ESP Hack

Um ESP (Extra Sensory Perception) completo para Counter-Strike 2, desenvolvido em C++ usando ImGui para interface e MinHook para hooking.

## Características

### ESP Features
- **Player Boxes**: Caixas ao redor dos jogadores
- **Health Bars**: Barras de vida coloridas
- **Player Names**: Nomes dos jogadores
- **Distance Display**: Dist<PERSON>cia até os jogadores
- **Team/Enemy Detection**: Diferenciação entre aliados e inimigos
- **Configurações Personalizáveis**: Cores, distância máxima, etc.

### Interface
- **Menu ImGui**: Interface moderna e intuitiva
- **Configurações em Tempo Real**: Ajuste todas as configurações sem reiniciar
- **Controles Simples**: INSERT para abrir/fechar menu, END para descarregar

## Estrutura do Projeto

```
dll cs2/
├── Dll1/Dll1/              # Projeto principal
│   ├── esp.h/cpp            # Classe principal do ESP
│   ├── memory.h/cpp         # Sistema de leitura de memória
│   ├── entity.h/cpp         # Gerenciamento de entidades
│   ├── render.h/cpp         # Sistema de renderização
│   ├── menu.h/cpp           # Interface do usuário
│   ├── offsets.h            # Offsets atualizados do CS2
│   └── dllmain.cpp          # Ponto de entrada da DLL
├── imgui-master/            # Biblioteca ImGui
├── minhook-master/          # Biblioteca MinHook
└── output/                  # Offsets e estruturas do CS2
```

## Compilação

### Pré-requisitos
- Visual Studio 2022 (ou 2019)
- Windows SDK 10.0
- DirectX 11 SDK (incluído no Windows SDK)

### Passos
1. Abra `Dll1/Dll1.sln` no Visual Studio
2. Selecione a configuração `Release x64`
3. Compile o projeto (Ctrl+Shift+B)
4. A DLL será gerada em `Dll1/x64/Release/Dll1.dll`

## Uso

### Injeção
1. Abra o CS2
2. Use um injetor de DLL para injetar `Dll1.dll` no processo `cs2.exe`
3. O ESP será inicializado automaticamente

### Controles
- **INSERT**: Abrir/fechar menu de configurações
- **END**: Descarregar o ESP

### Configurações
No menu você pode ajustar:
- Ativar/desativar ESP
- Mostrar/ocultar boxes, health, nomes, distância
- Mostrar/ocultar aliados
- Ajustar distância máxima
- Personalizar cores

## Offsets

Os offsets são atualizados automaticamente e estão localizados em:
- `output/offsets.hpp` - Offsets principais
- `output/client_dll.hpp` - Estruturas do client.dll
- `signatures.txt` - Assinaturas mais recentes

### Offsets Principais (2025-07-06)
```cpp
// Client.dll
dwEntityList = 0x1A044C0
dwLocalPlayerController = 0x1A52D00
dwLocalPlayerPawn = 0x18580D0
dwViewMatrix = 0x1A6D260

// Entity
m_iHealth = 0x344
m_iTeamNum = 0x3E3
m_pGameSceneNode = 0x328
m_vecAbsOrigin = 0xD0
```

## Funcionalidades Técnicas

### Sistema de Memória
- Leitura segura de memória do processo
- Validação de endereços
- Conversão World-to-Screen otimizada

### Sistema de Entidades
- Enumeração automática de jogadores
- Detecção de estado (vivo/morto)
- Cálculo de distâncias
- Diferenciação de times

### Sistema de Renderização
- Hook do DirectX 11 Present
- Renderização usando ImGui
- Overlay transparente
- Desenho de primitivas (linhas, retângulos, texto)

### Detecção de Inimigos
- Comparação de teams
- Filtros configuráveis
- Validação de entidades

## Segurança

### Medidas Implementadas
- Hooks seguros usando MinHook
- Validação de ponteiros
- Cleanup adequado na saída
- Sem modificação de arquivos do jogo

### Avisos
- Use por sua própria conta e risco
- Pode resultar em ban VAC
- Apenas para fins educacionais
- Teste em servidores offline primeiro

## Troubleshooting

### Problemas Comuns
1. **ESP não aparece**: Verifique se a DLL foi injetada corretamente
2. **Crash do jogo**: Offsets podem estar desatualizados
3. **Menu não abre**: Pressione INSERT, verifique se a DLL está carregada
4. **Boxes incorretas**: View matrix pode estar incorreta

### Debug
- Console é aberto automaticamente para logs
- Verifique mensagens de erro no console
- Logs mostram status de inicialização

## Atualizações

Para atualizar os offsets após updates do CS2:
1. Use o cs2-dumper para gerar novos offsets
2. Substitua os arquivos em `output/`
3. Atualize `offsets.h` se necessário
4. Recompile o projeto

## Créditos

- **ImGui**: Interface gráfica
- **MinHook**: Sistema de hooking
- **cs2-dumper**: Geração de offsets
- **Augment Agent**: Desenvolvimento do ESP

## Licença

Este projeto é apenas para fins educacionais. Use por sua própria conta e risco.
