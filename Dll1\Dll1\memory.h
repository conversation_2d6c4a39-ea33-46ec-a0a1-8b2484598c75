#pragma once
#include "pch.h"
#include <string>

// Estruturas matemáticas
struct Vector3
{
    float x, y, z;
    
    Vector3() : x(0), y(0), z(0) {}
    Vector3(float x, float y, float z) : x(x), y(y), z(z) {}
    
    Vector3 operator+(const Vector3& other) const
    {
        return Vector3(x + other.x, y + other.y, z + other.z);
    }
    
    Vector3 operator-(const Vector3& other) const
    {
        return Vector3(x - other.x, y - other.y, z - other.z);
    }
    
    float Length() const
    {
        return sqrt(x * x + y * y + z * z);
    }
    
    float Distance(const Vector3& other) const
    {
        return (*this - other).Length();
    }
};

struct Vector2
{
    float x, y;
    
    Vector2() : x(0), y(0) {}
    Vector2(float x, float y) : x(x), y(y) {}
};

struct ViewMatrix
{
    float matrix[4][4];
};

class Memory
{
private:
    HANDLE m_hProcess;
    DWORD m_dwProcessId;
    
public:
    Memory();
    ~Memory();
    
    bool Initialize();
    void Shutdown();
    
    // Leitura de memória
    template<typename T>
    T Read(uintptr_t address)
    {
        T value = {};
        ReadProcessMemory(GetCurrentProcess(), (LPCVOID)address, &value, sizeof(T), nullptr);
        return value;
    }
    
    // Leitura de string
    std::string ReadString(uintptr_t address, size_t maxLength = 256);
    
    // Leitura de ponteiro
    uintptr_t ReadPointer(uintptr_t address)
    {
        return Read<uintptr_t>(address);
    }
    
    // Obter endereço do módulo
    uintptr_t GetModuleBase(const char* moduleName);
    
    // Verificar se endereço é válido
    bool IsValidAddress(uintptr_t address);
    
    // Conversão World to Screen
    bool WorldToScreen(const Vector3& worldPos, Vector2& screenPos, const ViewMatrix& viewMatrix, int screenWidth, int screenHeight);
};
