#include "pch.h"
#include "render.h"

// Incluir ImGui via wrapper
#include "imgui_wrapper.h"

// Incluir MinHook
#include "../../minhook-master/minhook-master/include/MinHook.h"

// Variáveis estáticas
Render::Present_t Render::oPresent = nullptr;
Render::WndProc_t Render::oWndProc = nullptr;
Render* Render::s_pInstance = nullptr;

Render::Render() : 
    m_pd3dDevice(nullptr),
    m_pd3dDeviceContext(nullptr),
    m_pSwapChain(nullptr),
    m_pMainRenderTargetView(nullptr),
    m_pFont(nullptr),
    m_bInitialized(false),
    m_iScreenWidth(0),
    m_iScreenHeight(0),
    m_hWnd(nullptr)
{
    s_pInstance = this;
}

Render::~Render()
{
    Shutdown();
    s_pInstance = nullptr;
}

bool Render::Initialize()
{
    printf("[Render] Inicializando sistema de renderização...\n");
    
    // Inicializar <PERSON>Hook
    if (MH_Initialize() != MH_OK)
    {
        printf("[Render] Falha ao inicializar MinHook!\n");
        return false;
    }
    
    // Encontrar janela do CS2
    m_hWnd = FindWindowA("SDL_app", nullptr);
    if (!m_hWnd)
    {
        printf("[Render] Falha ao encontrar janela do CS2!\n");
        return false;
    }
    
    // Obter dimensões da tela
    RECT rect;
    GetClientRect(m_hWnd, &rect);
    m_iScreenWidth = rect.right - rect.left;
    m_iScreenHeight = rect.bottom - rect.top;
    
    printf("[Render] Janela encontrada: %dx%d\n", m_iScreenWidth, m_iScreenHeight);
    
    // Inicializar DirectX
    if (!InitializeDirectX())
    {
        printf("[Render] Falha ao inicializar DirectX!\n");
        return false;
    }
    
    // Inicializar ImGui
    if (!InitializeImGui())
    {
        printf("[Render] Falha ao inicializar ImGui!\n");
        return false;
    }
    
    // Hook DirectX
    if (!HookDirectX())
    {
        printf("[Render] Falha ao fazer hook do DirectX!\n");
        return false;
    }
    
    m_bInitialized = true;
    printf("[Render] Sistema de renderização inicializado com sucesso!\n");
    return true;
}

void Render::Shutdown()
{
    if (m_bInitialized)
    {
        UnhookDirectX();
        CleanupImGui();
        CleanupDirectX();
        
        MH_Uninitialize();
        m_bInitialized = false;
    }
}

bool Render::InitializeDirectX()
{
    // Criar device e swap chain temporários para obter o vtable
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = m_hWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;
    
    UINT createDeviceFlags = 0;
    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0, };
    
    HRESULT hr = D3D11CreateDeviceAndSwapChain(
        nullptr,
        D3D_DRIVER_TYPE_HARDWARE,
        nullptr,
        createDeviceFlags,
        featureLevelArray,
        2,
        D3D11_SDK_VERSION,
        &sd,
        &m_pSwapChain,
        &m_pd3dDevice,
        &featureLevel,
        &m_pd3dDeviceContext
    );
    
    if (FAILED(hr))
    {
        printf("[Render] Falha ao criar device DirectX: 0x%08X\n", hr);
        return false;
    }
    
    CreateRenderTarget();
    return true;
}

bool Render::InitializeImGui()
{
    // Setup ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    
    // Setup ImGui style
    ImGui::StyleColorsDark();
    
    // Setup Platform/Renderer backends
    ImGui_ImplWin32_Init(m_hWnd);
    ImGui_ImplDX11_Init(m_pd3dDevice, m_pd3dDeviceContext);
    
    return true;
}

void Render::CleanupDirectX()
{
    CleanupRenderTarget();
    
    if (m_pSwapChain) { m_pSwapChain->Release(); m_pSwapChain = nullptr; }
    if (m_pd3dDeviceContext) { m_pd3dDeviceContext->Release(); m_pd3dDeviceContext = nullptr; }
    if (m_pd3dDevice) { m_pd3dDevice->Release(); m_pd3dDevice = nullptr; }
}

void Render::CleanupImGui()
{
    ImGui_ImplDX11_Shutdown();
    ImGui_ImplWin32_Shutdown();
    ImGui::DestroyContext();
}

void Render::CreateRenderTarget()
{
    ID3D11Texture2D* pBackBuffer;
    m_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    if (pBackBuffer)
    {
        m_pd3dDevice->CreateRenderTargetView(pBackBuffer, nullptr, &m_pMainRenderTargetView);
        pBackBuffer->Release();
    }
}

void Render::CleanupRenderTarget()
{
    if (m_pMainRenderTargetView) { m_pMainRenderTargetView->Release(); m_pMainRenderTargetView = nullptr; }
}

bool Render::HookDirectX()
{
    // Obter endereço do Present
    void** pVTable = *reinterpret_cast<void***>(m_pSwapChain);
    void* pPresent = pVTable[8]; // Present está no índice 8
    
    // Hook Present
    if (MH_CreateHook(pPresent, &hkPresent, reinterpret_cast<LPVOID*>(&oPresent)) != MH_OK)
    {
        printf("[Render] Falha ao criar hook do Present!\n");
        return false;
    }
    
    if (MH_EnableHook(pPresent) != MH_OK)
    {
        printf("[Render] Falha ao ativar hook do Present!\n");
        return false;
    }
    
    // Hook WndProc
    oWndProc = (WndProc_t)SetWindowLongPtr(m_hWnd, GWLP_WNDPROC, (LONG_PTR)hkWndProc);
    
    return true;
}

void Render::UnhookDirectX()
{
    // Restaurar WndProc
    if (oWndProc)
    {
        SetWindowLongPtr(m_hWnd, GWLP_WNDPROC, (LONG_PTR)oWndProc);
        oWndProc = nullptr;
    }
    
    // Desabilitar hooks
    MH_DisableHook(MH_ALL_HOOKS);
}

HRESULT __stdcall Render::hkPresent(IDXGISwapChain* pSwapChain, UINT SyncInterval, UINT Flags)
{
    if (s_pInstance && s_pInstance->m_bInitialized)
    {
        s_pInstance->OnPresent();
    }
    
    return oPresent(pSwapChain, SyncInterval, Flags);
}

// Forward declaration já está no wrapper

LRESULT __stdcall Render::hkWndProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
    if (ImGui_ImplWin32_WndProcHandler(hWnd, uMsg, wParam, lParam))
        return true;
    
    return CallWindowProc(oWndProc, hWnd, uMsg, wParam, lParam);
}

void Render::OnPresent()
{
    // Start ImGui frame
    ImGui_ImplDX11_NewFrame();
    ImGui_ImplWin32_NewFrame();
    ImGui::NewFrame();

    // O ESP será renderizado aqui através do sistema de callbacks
    // Isso é feito automaticamente pelo ImGui::GetBackgroundDrawList()

    // Render ImGui
    ImGui::Render();
    m_pd3dDeviceContext->OMSetRenderTargets(1, &m_pMainRenderTargetView, nullptr);
    ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());
}

void Render::BeginFrame()
{
    // Não necessário, já feito no OnPresent
}

void Render::EndFrame()
{
    // Não necessário, já feito no OnPresent
}

void Render::DrawLine(const Vector2& from, const Vector2& to, uint32_t color, float thickness)
{
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    drawList->AddLine(ImVec2(from.x, from.y), ImVec2(to.x, to.y), color, thickness);
}

void Render::DrawRect(const Vector2& pos, const Vector2& size, uint32_t color, float thickness)
{
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    drawList->AddRect(ImVec2(pos.x, pos.y), ImVec2(pos.x + size.x, pos.y + size.y), color, 0.0f, 0, thickness);
}

void Render::DrawFilledRect(const Vector2& pos, const Vector2& size, uint32_t color)
{
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    drawList->AddRectFilled(ImVec2(pos.x, pos.y), ImVec2(pos.x + size.x, pos.y + size.y), color);
}

void Render::DrawText(const Vector2& pos, const char* text, uint32_t color, float fontSize)
{
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();

    if (fontSize != 14.0f)
    {
        // Para tamanhos diferentes, usar AddText com tamanho específico
        ImFont* font = ImGui::GetFont();
        if (font)
        {
            drawList->AddText(font, fontSize, ImVec2(pos.x, pos.y), color, text);
        }
        else
        {
            drawList->AddText(ImVec2(pos.x, pos.y), color, text);
        }
    }
    else
    {
        // Tamanho padrão
        drawList->AddText(ImVec2(pos.x, pos.y), color, text);
    }
}

void Render::DrawCircle(const Vector2& center, float radius, uint32_t color, int segments)
{
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    drawList->AddCircle(ImVec2(center.x, center.y), radius, color, segments);
}

void Render::DrawFilledCircle(const Vector2& center, float radius, uint32_t color, int segments)
{
    ImDrawList* drawList = ImGui::GetBackgroundDrawList();
    drawList->AddCircleFilled(ImVec2(center.x, center.y), radius, color, segments);
}

void Render::DrawPlayerBox(const Vector2& head, const Vector2& feet, uint32_t color, float thickness)
{
    float height = feet.y - head.y;
    float width = height * 0.4f; // Proporção da caixa

    Vector2 topLeft = Vector2(head.x - width / 2, head.y);
    Vector2 size = Vector2(width, height);

    DrawRect(topLeft, size, color, thickness);
}

void Render::DrawHealthBar(const Vector2& pos, const Vector2& size, int health, int maxHealth)
{
    if (maxHealth <= 0) return;

    float healthPercent = (float)health / (float)maxHealth;
    healthPercent = max(0.0f, min(1.0f, healthPercent));

    // Background da barra
    uint32_t bgColor = ColorToUInt32(0.2f, 0.2f, 0.2f, 0.8f);
    DrawFilledRect(pos, size, bgColor);

    // Barra de vida
    uint32_t healthColor;
    if (healthPercent > 0.6f)
        healthColor = ColorToUInt32(0.0f, 1.0f, 0.0f, 1.0f); // Verde
    else if (healthPercent > 0.3f)
        healthColor = ColorToUInt32(1.0f, 1.0f, 0.0f, 1.0f); // Amarelo
    else
        healthColor = ColorToUInt32(1.0f, 0.0f, 0.0f, 1.0f); // Vermelho

    Vector2 healthSize = Vector2(size.x, size.y * healthPercent);
    Vector2 healthPos = Vector2(pos.x, pos.y + size.y - healthSize.y);

    DrawFilledRect(healthPos, healthSize, healthColor);

    // Borda
    uint32_t borderColor = ColorToUInt32(0.0f, 0.0f, 0.0f, 1.0f);
    DrawRect(pos, size, borderColor, 1.0f);
}

void Render::DrawPlayerInfo(const Vector2& pos, const char* name, int health, int distance, uint32_t color)
{
    if (!name || strlen(name) == 0)
        return;

    // Usar função auxiliar para texto com outline
    uint32_t outlineColor = ColorToUInt32(0.0f, 0.0f, 0.0f, 0.8f);

    // Centralizar o texto horizontalmente
    Vector2 textSize = GetTextSize(name, 14.0f);
    Vector2 centeredPos = Vector2(pos.x - textSize.x / 2, pos.y);

    // Desenhar texto com outline
    DrawTextWithOutline(centeredPos, name, color, outlineColor, 14.0f);
}

uint32_t Render::ColorToUInt32(float r, float g, float b, float a)
{
    return IM_COL32((int)(r * 255), (int)(g * 255), (int)(b * 255), (int)(a * 255));
}

uint32_t Render::ColorToUInt32(const float color[4])
{
    return ColorToUInt32(color[0], color[1], color[2], color[3]);
}

Vector2 Render::GetTextSize(const char* text, float fontSize)
{
    if (!text || strlen(text) == 0)
        return Vector2(0, 0);

    ImVec2 size = ImGui::CalcTextSize(text);

    // Ajustar para fontSize se diferente do padrão
    if (fontSize != 14.0f)
    {
        float scale = fontSize / 14.0f;
        size.x *= scale;
        size.y *= scale;
    }

    return Vector2(size.x, size.y);
}

void Render::DrawTextCentered(const Vector2& pos, const char* text, uint32_t color, float fontSize)
{
    if (!text || strlen(text) == 0)
        return;

    Vector2 textSize = GetTextSize(text, fontSize);
    Vector2 centeredPos = Vector2(pos.x - textSize.x / 2, pos.y - textSize.y / 2);

    DrawText(centeredPos, text, color, fontSize);
}

void Render::DrawTextWithOutline(const Vector2& pos, const char* text, uint32_t textColor, uint32_t outlineColor, float fontSize)
{
    if (!text || strlen(text) == 0)
        return;

    // Desenhar outline (8 direções)
    for (int x = -1; x <= 1; x++)
    {
        for (int y = -1; y <= 1; y++)
        {
            if (x == 0 && y == 0) continue;
            DrawText(Vector2(pos.x + x, pos.y + y), text, outlineColor, fontSize);
        }
    }

    // Desenhar texto principal
    DrawText(pos, text, textColor, fontSize);
}
