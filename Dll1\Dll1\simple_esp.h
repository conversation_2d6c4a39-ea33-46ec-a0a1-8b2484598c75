#pragma once
#include "pch.h"
#include "memory.h"
#include "pattern_scanner.h"
#include "offset_finder.h"

// ESP simplificado sem hooks para evitar crashes
class SimpleESP
{
private:
    Memory* m_pMemory;
    PatternScanner* m_pScanner;
    HMODULE m_hClient;
    HMODULE m_hEngine;
    
    // Offsets básicos
    uintptr_t m_dwEntityList;
    uintptr_t m_dwLocalPlayerController;
    uintptr_t m_dwLocalPlayerPawn;
    uintptr_t m_dwViewMatrix;
    
    bool m_bRunning;
    
public:
    SimpleESP();
    ~SimpleESP();
    
    bool Initialize();
    void Shutdown();
    void Run(); // Loop principal sem hooks
    
private:
    bool FindModules();
    bool FindOffsets();
    bool FindOffsetsWithPatterns(); // Novo método com pattern scanning
    void UpdateEntities();
    void PrintEntityInfo();
    bool IsValidPointer(uintptr_t ptr);
};
