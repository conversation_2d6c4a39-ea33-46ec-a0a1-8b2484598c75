#pragma once
#include "pch.h"
#include "memory.h"
#include "pattern_scanner.h"
#include "entity_manager.h"

// ESP simplificado sem hooks para evitar crashes
class SimpleESP
{
private:
    Memory* m_pMemory;
    PatternScanner* m_pScanner;
    EntityManager* m_pEntityManager;
    HMODULE m_hClient;
    HMODULE m_hEngine;
    
    // Offsets básicos
    uintptr_t m_dwEntityList;
    uintptr_t m_dwLocalPlayerController;
    uintptr_t m_dwLocalPlayerPawn;
    uintptr_t m_dwViewMatrix;

    // Offsets de estruturas (dinâmicos)
    uintptr_t m_offsetPlayerPawn;
    uintptr_t m_offsetPawnHealth;
    uintptr_t m_offsetPawnAlive;
    uintptr_t m_offsetHealth;
    uintptr_t m_offsetTeam;
    
    bool m_bRunning;
    
public:
    SimpleESP();
    ~SimpleESP();
    
    bool Initialize();
    void Shutdown();
    void Run(); // Loop principal sem hooks
    
private:
    bool FindModules();
    bool FindOffsets();
    bool FindOffsetsWithPatterns();
    void UpdateEntities();
    void PrintPlayerInfo();
    bool IsValidPointer(uintptr_t ptr);
};
