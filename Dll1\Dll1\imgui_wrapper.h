#pragma once

// Wrapper para includes do ImGui
// Resolve problemas de caminho e configuração

// Incluir headers principais do ImGui com caminhos absolutos
#include "../../imgui-master/imgui-master/imgui.h"
#include "../../imgui-master/imgui-master/imgui_internal.h"

// Incluir backends
#include "../../imgui-master/imgui-master/backends/imgui_impl_win32.h"
#include "../../imgui-master/imgui-master/backends/imgui_impl_dx11.h"

// Forward declarations úteis
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);
