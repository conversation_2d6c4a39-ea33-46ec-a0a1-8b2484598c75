#include "pch.h"
#include "pattern_scanner.h"

PatternScanner::PatternScanner(uintptr_t moduleBase) : m_moduleBase(moduleBase), m_moduleSize(0)
{
}

PatternScanner::~PatternScanner()
{
}

bool PatternScanner::Initialize()
{
    m_moduleSize = GetModuleSize(m_moduleBase);
    if (m_moduleSize == 0)
    {
        printf("[PatternScanner] Falha ao obter tamanho do módulo!\n");
        return false;
    }
    
    printf("[PatternScanner] Módulo: 0x%p, Tamanho: 0x%X\n", (void*)m_moduleBase, (uint32_t)m_moduleSize);
    return true;
}

uintptr_t PatternScanner::FindPattern(const char* pattern, const char* mask)
{
    size_t patternLength = strlen(mask);
    uint8_t* moduleBytes = (uint8_t*)m_moduleBase;
    
    for (size_t i = 0; i <= m_moduleSize - patternLength; i++)
    {
        if (CompareBytes(moduleBytes + i, (uint8_t*)pattern, mask))
        {
            return m_moduleBase + i;
        }
    }
    
    return 0;
}

uintptr_t PatternScanner::FindPatternIDA(const char* pattern)
{
    std::vector<uint8_t> patternBytes = ParseIDAPattern(pattern);
    if (patternBytes.empty())
        return 0;
    
    uint8_t* moduleBytes = (uint8_t*)m_moduleBase;
    
    for (size_t i = 0; i <= m_moduleSize - patternBytes.size(); i++)
    {
        bool found = true;
        for (size_t j = 0; j < patternBytes.size(); j++)
        {
            if (patternBytes[j] != 0xFF && moduleBytes[i + j] != patternBytes[j])
            {
                found = false;
                break;
            }
        }
        
        if (found)
        {
            return m_moduleBase + i;
        }
    }
    
    return 0;
}

uintptr_t PatternScanner::FindEntityList()
{
    printf("[PatternScanner] Procurando EntityList...\n");
    
    // Patterns conhecidos para EntityList
    const char* patterns[] = {
        "48 8B 0D ? ? ? ? 48 89 7C 24 ? 8B FA C1 EB",           // Pattern 1
        "48 8B 0D ? ? ? ? 8B C5 25 ? ? ? ? 48 6B C0 78",        // Pattern 2
        "48 8B 0D ? ? ? ? 48 C1 E0 06 48 03 C1 C3",             // Pattern 3
    };
    
    for (int i = 0; i < 3; i++)
    {
        uintptr_t result = FindPatternIDA(patterns[i]);
        if (result)
        {
            // Ler offset relativo
            int32_t offset = *(int32_t*)(result + 3);
            uintptr_t entityList = result + 7 + offset;
            
            printf("[PatternScanner] EntityList encontrado com pattern %d: 0x%p\n", i + 1, (void*)entityList);
            return entityList;
        }
    }
    
    printf("[PatternScanner] EntityList não encontrado!\n");
    return 0;
}

uintptr_t PatternScanner::FindLocalPlayerController()
{
    printf("[PatternScanner] Procurando LocalPlayerController...\n");
    
    const char* patterns[] = {
        "48 8B 05 ? ? ? ? 48 85 C0 74 ? 48 8B 88",              // Pattern 1
        "48 8B 0D ? ? ? ? 48 85 C9 0F 84 ? ? ? ? 8B 81",        // Pattern 2
    };
    
    for (int i = 0; i < 2; i++)
    {
        uintptr_t result = FindPatternIDA(patterns[i]);
        if (result)
        {
            int32_t offset = *(int32_t*)(result + 3);
            uintptr_t localController = result + 7 + offset;
            
            printf("[PatternScanner] LocalPlayerController encontrado: 0x%p\n", (void*)localController);
            return localController;
        }
    }
    
    printf("[PatternScanner] LocalPlayerController não encontrado!\n");
    return 0;
}

uintptr_t PatternScanner::FindLocalPlayerPawn()
{
    printf("[PatternScanner] Procurando LocalPlayerPawn...\n");
    
    const char* patterns[] = {
        "48 8B 05 ? ? ? ? 48 85 C0 74 ? 48 8B 80 ? ? ? ?",      // Pattern 1
        "48 89 05 ? ? ? ? 48 8B 0D ? ? ? ? 48 85 C9",           // Pattern 2
    };
    
    for (int i = 0; i < 2; i++)
    {
        uintptr_t result = FindPatternIDA(patterns[i]);
        if (result)
        {
            int32_t offset = *(int32_t*)(result + 3);
            uintptr_t localPawn = result + 7 + offset;
            
            printf("[PatternScanner] LocalPlayerPawn encontrado: 0x%p\n", (void*)localPawn);
            return localPawn;
        }
    }
    
    printf("[PatternScanner] LocalPlayerPawn não encontrado!\n");
    return 0;
}

uintptr_t PatternScanner::FindViewMatrix()
{
    printf("[PatternScanner] Procurando ViewMatrix...\n");
    
    const char* patterns[] = {
        "48 8D 0D ? ? ? ? 48 C1 E0 06 48 03 C1 C3",             // Pattern 1
        "0F 10 05 ? ? ? ? 8D 47 01 0F 11 45",                   // Pattern 2
    };
    
    for (int i = 0; i < 2; i++)
    {
        uintptr_t result = FindPatternIDA(patterns[i]);
        if (result)
        {
            int32_t offset = *(int32_t*)(result + 3);
            uintptr_t viewMatrix = result + 7 + offset;
            
            printf("[PatternScanner] ViewMatrix encontrado: 0x%p\n", (void*)viewMatrix);
            return viewMatrix;
        }
    }
    
    printf("[PatternScanner] ViewMatrix não encontrado!\n");
    return 0;
}

bool PatternScanner::CompareBytes(const uint8_t* data, const uint8_t* pattern, const char* mask)
{
    for (size_t i = 0; mask[i]; i++)
    {
        if (mask[i] == 'x' && data[i] != pattern[i])
            return false;
    }
    return true;
}

std::vector<uint8_t> PatternScanner::ParseIDAPattern(const char* pattern)
{
    std::vector<uint8_t> result;
    std::string patternStr(pattern);
    
    for (size_t i = 0; i < patternStr.length(); i++)
    {
        if (patternStr[i] == ' ')
            continue;
        
        if (patternStr[i] == '?')
        {
            result.push_back(0xFF); // Wildcard
            if (i + 1 < patternStr.length() && patternStr[i + 1] == '?')
                i++; // Skip second ?
        }
        else
        {
            // Parse hex byte
            if (i + 1 < patternStr.length())
            {
                std::string byteStr = patternStr.substr(i, 2);
                uint8_t byte = (uint8_t)strtol(byteStr.c_str(), nullptr, 16);
                result.push_back(byte);
                i++; // Skip next character
            }
        }
    }
    
    return result;
}

size_t PatternScanner::GetModuleSize(uintptr_t moduleBase)
{
    MEMORY_BASIC_INFORMATION mbi;
    size_t totalSize = 0;
    uintptr_t currentAddress = moduleBase;
    
    while (VirtualQuery((LPCVOID)currentAddress, &mbi, sizeof(mbi)))
    {
        if (mbi.AllocationBase != (LPVOID)moduleBase)
            break;
        
        totalSize += mbi.RegionSize;
        currentAddress += mbi.RegionSize;
    }
    
    return totalSize;
}
