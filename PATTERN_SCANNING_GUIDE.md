# 🔍 Pattern Scanning - CS2 ESP

## ✅ Problema Identificado e Solução

### ❌ Problema Original
- **Offsets fixos incorretos** (dados de lixo de memória)
- **Health = 967251552** (valores absurdos)
- **Team = 0** (sempre zero)
- **Handles inválidos** (0x28D, etc.)

### ✅ Solução Implementada
- **Pattern Scanning dinâmico** para encontrar offsets corretos
- **Múltiplos patterns** para cada offset
- **Fallback** para offsets fixos se pattern scanning falhar
- **Validação** de resultados encontrados

## 🔍 Como Funciona o Pattern Scanning

### **1. Busca por Assinaturas**
```cpp
// Exemplo de pattern para EntityList
"48 8B 0D ? ? ? ? 48 89 7C 24 ? 8B FA C1 EB"
```
- **48 8B 0D**: Instrução específica
- **? ? ? ?**: Wildcards (qualquer valor)
- **Encontra**: Referência ao EntityList no código

### **2. Resolução de Offsets**
```cpp
// Lê offset relativo da instrução
int32_t offset = *(int32_t*)(result + 3);
uintptr_t entityList = result + 7 + offset;
```
- **result + 3**: Posição do offset na instrução
- **result + 7**: Próxima instrução
- **+ offset**: Endereço final do EntityList

### **3. Múltiplos Patterns**
- **Pattern 1**: Método principal
- **Pattern 2**: Método alternativo
- **Pattern 3**: Backup adicional

## 🚀 Teste da Nova Versão

### **1. Recompilar**
```bash
# Compile com pattern scanning
build.bat
```

### **2. Logs Esperados**
```
[SimpleESP] === PATTERN SCANNING ===
[PatternScanner] Módulo: 0xXXXXXXXX, Tamanho: 0xXXXXXX
[PatternScanner] Procurando EntityList...
[PatternScanner] EntityList encontrado com pattern 1: 0xXXXXXXXX
[PatternScanner] Procurando LocalPlayerController...
[PatternScanner] LocalPlayerController encontrado: 0xXXXXXXXX
[PatternScanner] Procurando LocalPlayerPawn...
[PatternScanner] LocalPlayerPawn encontrado: 0xXXXXXXXX
[PatternScanner] Procurando ViewMatrix...
[PatternScanner] ViewMatrix encontrado: 0xXXXXXXXX

[SimpleESP] === OFFSETS ENCONTRADOS ===
  EntityList: 0xXXXXXXXX (PATTERN)
  LocalController: 0xXXXXXXXX (PATTERN)
  LocalPawn: 0xXXXXXXXX (PATTERN)
  ViewMatrix: 0xXXXXXXXX (PATTERN)
```

### **3. Resultados Esperados**
Com offsets corretos, você deve ver:
```
[SimpleESP] Status: EM JOGO | LocalController: 0xXXXX, LocalPawn: 0xXXXX
[SimpleESP] === ANALISE DE ENTIDADES ===
[SimpleESP] JOGADOR[1]:
  Controller: 0xXXXXXXXX
  PawnHandle: 0x80000001 -> Pawn: 0xXXXXXXXX
  Health: 100 (Controller: 100)
  Team: 2, Alive: Yes

[SimpleESP] JOGADOR[2]:
  Controller: 0xXXXXXXXX
  PawnHandle: 0x80000002 -> Pawn: 0xXXXXXXXX
  Health: 85 (Controller: 85)
  Team: 3, Alive: Yes

[SimpleESP] === RESULTADO ===
[SimpleESP] Controllers válidos: X
[SimpleESP] Jogadores reais: X
```

## 📊 Comparação: Antes vs Depois

### **❌ Antes (Offsets Fixos)**
```
Health: 0/967251552    # Lixo de memória
Team: 0               # Sempre zero
Handle: 0x28D         # Inválido
Pawn: 0x000000000000028D  # Endereço inválido
```

### **✅ Depois (Pattern Scanning)**
```
Health: 100/100       # Valores reais
Team: 2               # Terrorista/CT
Handle: 0x80000001    # Handle válido
Pawn: 0x0000028XXXXXXX  # Endereço válido
```

## 🔧 Patterns Implementados

### **EntityList Patterns**
1. `48 8B 0D ? ? ? ? 48 89 7C 24 ? 8B FA C1 EB`
2. `48 8B 0D ? ? ? ? 8B C5 25 ? ? ? ? 48 6B C0 78`
3. `48 8B 0D ? ? ? ? 48 C1 E0 06 48 03 C1 C3`

### **LocalPlayerController Patterns**
1. `48 8B 05 ? ? ? ? 48 85 C0 74 ? 48 8B 88`
2. `48 8B 0D ? ? ? ? 48 85 C9 0F 84 ? ? ? ? 8B 81`

### **LocalPlayerPawn Patterns**
1. `48 8B 05 ? ? ? ? 48 85 C0 74 ? 48 8B 80 ? ? ? ?`
2. `48 89 05 ? ? ? ? 48 8B 0D ? ? ? ? 48 85 C9`

### **ViewMatrix Patterns**
1. `48 8D 0D ? ? ? ? 48 C1 E0 06 48 03 C1 C3`
2. `0F 10 05 ? ? ? ? 8D 47 01 0F 11 45`

## 🎯 Cenários de Teste

### **Cenário 1: Pattern Scanning Sucesso**
- **Todos os patterns** encontrados
- **Offsets dinâmicos** usados
- **Dados válidos** de jogadores

### **Cenário 2: Pattern Scanning Parcial**
- **Alguns patterns** encontrados
- **Mix de dinâmico/fixo** usado
- **Funcionalidade básica** mantida

### **Cenário 3: Pattern Scanning Falha**
- **Nenhum pattern** encontrado
- **Offsets fixos** como fallback
- **Pode não funcionar** se CS2 foi atualizado

## 🚨 Troubleshooting

### **Se Pattern Scanning Falhar**
```
[SimpleESP] Pattern scanning falhou, usando offsets fixos...
```
**Solução**: CS2 foi atualizado, patterns precisam ser atualizados

### **Se Ainda Não Encontrar Jogadores**
```
[SimpleESP] Jogadores reais: 0
```
**Possíveis causas**:
1. **Offsets de estruturas** incorretos (m_iHealth, m_iTeamNum)
2. **Método de resolução** de pawn handle incorreto
3. **Validação muito restritiva**

### **Se Dados Ainda Estão Incorretos**
```
Health: 0, Team: 0
```
**Solução**: Atualizar offsets das estruturas internas

## 📞 Teste e Reporte

### **Informações Necessárias**
1. **Pattern scanning funcionou?** (PATTERN vs FIXO nos logs)
2. **Quantos patterns encontrados?** (EntityList, LocalPlayer, etc.)
3. **Dados dos jogadores corretos?** (Health 1-100, Team 2-3)
4. **Handles válidos?** (0x80000001, 0x80000002, etc.)

### **Logs Importantes**
- **Pattern scanning** completo
- **Offsets encontrados** (PATTERN vs FIXO)
- **Análise de entidades** com dados reais

---

## 🎉 Objetivo

**Encontrar offsets corretos dinamicamente e detectar jogadores reais com dados válidos!**

**Teste agora e veja se os dados fazem sentido!** 🔍✨
