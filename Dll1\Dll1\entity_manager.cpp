#include "pch.h"
#include "entity_manager.h"
#include <functional>

EntityManager::EntityManager(Memory* memory) 
    : m_memory(memory), m_entityList(0), m_localController(0), m_localPawn(0), m_playerCount(0)
{
}

EntityManager::~EntityManager()
{
}

bool EntityManager::Initialize(uintptr_t entityList, uintptr_t localController, uintptr_t localPawn)
{
    m_entityList = entityList;
    m_localController = localController;
    m_localPawn = localPawn;
    
    printf("[EntityManager] Inicializando...\n");
    printf("[EntityManager] EntityList: 0x%p\n", (void*)entityList);
    printf("[EntityManager] LocalController: 0x%p\n", (void*)localController);
    printf("[EntityManager] LocalPawn: 0x%p\n", (void*)localPawn);
    
    if (!FindOffsets())
    {
        printf("[EntityManager] Falha ao encontrar offsets!\n");
        return false;
    }
    
    printf("[EntityManager] Inicializado com sucesso!\n");
    return true;
}

bool EntityManager::FindOffsets()
{
    printf("[EntityManager] === ENCONTRANDO OFFSETS ===\n");
    
    // Usar LocalPawn como referência para offsets do pawn
    if (m_localPawn != 0)
    {
        // Encontrar m_iHealth no pawn
        uintptr_t healthOffsets[] = { 0x344, 0x340, 0x348, 0x33C, 0x350, 0x354, 0x338 };
        for (int i = 0; i < 7; i++)
        {
            int health = m_memory->Read<int>(m_localPawn + healthOffsets[i]);
            if (health > 0 && health <= 100)
            {
                offsets.m_iHealth = healthOffsets[i];
                printf("[EntityManager] m_iHealth (pawn): 0x%X = %d\n", healthOffsets[i], health);
                break;
            }
        }
        
        // Encontrar m_iTeamNum no pawn
        uintptr_t teamOffsets[] = { 0x3E3, 0x3E0, 0x3E4, 0x3E8, 0x3DC, 0x3EC };
        for (int i = 0; i < 6; i++)
        {
            int team = m_memory->Read<int>(m_localPawn + teamOffsets[i]);
            if (team == 2 || team == 3)
            {
                offsets.m_iTeamNum_Pawn = teamOffsets[i];
                printf("[EntityManager] m_iTeamNum (pawn): 0x%X = %d\n", teamOffsets[i], team);
                break;
            }
        }
        
        // Encontrar posição (m_vOldOrigin ou similar)
        uintptr_t posOffsets[] = { 0x1324, 0x1320, 0x1328, 0x132C, 0x1330 };
        for (int i = 0; i < 5; i++)
        {
            Vector3 pos = m_memory->Read<Vector3>(m_localPawn + posOffsets[i]);
            if (pos.x != 0.0f || pos.y != 0.0f || pos.z != 0.0f)
            {
                offsets.m_vOldOrigin = posOffsets[i];
                printf("[EntityManager] m_vOldOrigin: 0x%X = (%.1f, %.1f, %.1f)\n", 
                       posOffsets[i], pos.x, pos.y, pos.z);
                break;
            }
        }
    }
    
    // Usar LocalController como referência para offsets do controller
    if (m_localController != 0)
    {
        // Encontrar m_hPlayerPawn
        uintptr_t pawnHandleOffsets[] = { 0x824, 0x7FC, 0x800, 0x804, 0x808, 0x80C, 0x810, 0x814, 0x818, 0x81C, 0x820, 0x828 };
        for (int i = 0; i < 12; i++)
        {
            uintptr_t handle = m_memory->Read<uintptr_t>(m_localController + pawnHandleOffsets[i]);
            if (handle != 0 && handle != 0xFFFFFFFF && (handle & 0x7FFFFFFF) < 2048)
            {
                offsets.m_hPlayerPawn = pawnHandleOffsets[i];
                printf("[EntityManager] m_hPlayerPawn: 0x%X = 0x%X\n", pawnHandleOffsets[i], handle);
                break;
            }
        }
        
        // Encontrar m_iPawnHealth no controller
        uintptr_t controllerHealthOffsets[] = { 0x830, 0x834, 0x838, 0x82C, 0x828, 0x820, 0x83C };
        for (int i = 0; i < 7; i++)
        {
            int health = m_memory->Read<int>(m_localController + controllerHealthOffsets[i]);
            if (health > 0 && health <= 100)
            {
                offsets.m_iPawnHealth = controllerHealthOffsets[i];
                printf("[EntityManager] m_iPawnHealth (controller): 0x%X = %d\n", controllerHealthOffsets[i], health);
                break;
            }
        }
        
        // Encontrar m_bPawnIsAlive
        uintptr_t aliveOffsets[] = { 0x82C, 0x828, 0x824, 0x830, 0x834, 0x820 };
        for (int i = 0; i < 6; i++)
        {
            bool alive = m_memory->Read<bool>(m_localController + aliveOffsets[i]);
            if (alive) // Assumindo que o jogador local está vivo
            {
                offsets.m_bPawnIsAlive = aliveOffsets[i];
                printf("[EntityManager] m_bPawnIsAlive: 0x%X = %s\n", aliveOffsets[i], alive ? "true" : "false");
                break;
            }
        }
    }
    
    offsets.initialized = true;
    printf("[EntityManager] === OFFSETS ENCONTRADOS ===\n");
    return true;
}

void EntityManager::Update()
{
    if (!offsets.initialized)
        return;
    
    // Atualizar jogador local
    UpdateLocalPlayer();
    
    // Atualizar outros jogadores
    UpdatePlayers();
}

bool EntityManager::UpdateLocalPlayer()
{
    if (m_localController == 0 || m_localPawn == 0)
        return false;
    
    m_localPlayer.controller = m_localController;
    m_localPlayer.pawn = m_localPawn;
    
    // Ler dados do pawn local
    if (offsets.m_iHealth != 0)
        m_localPlayer.health = m_memory->Read<int>(m_localPawn + offsets.m_iHealth);
    
    if (offsets.m_iTeamNum_Pawn != 0)
        m_localPlayer.team = m_memory->Read<int>(m_localPawn + offsets.m_iTeamNum_Pawn);
    
    if (offsets.m_bPawnIsAlive != 0)
        m_localPlayer.alive = m_memory->Read<bool>(m_localController + offsets.m_bPawnIsAlive);
    
    if (offsets.m_vOldOrigin != 0)
        m_localPlayer.position = m_memory->Read<Vector3>(m_localPawn + offsets.m_vOldOrigin);
    
    m_localPlayer.valid = (m_localPlayer.health > 0 && m_localPlayer.health <= 100) &&
                         (m_localPlayer.team == 2 || m_localPlayer.team == 3);
    
    return m_localPlayer.valid;
}

bool EntityManager::UpdatePlayers()
{
    m_playerCount = 0;

    printf("[EntityManager] === BUSCANDO OUTROS JOGADORES ===\n");

    // Método 1: Buscar controllers na EntityList
    for (int i = 1; i <= 64; i++)
    {
        uintptr_t controller = m_memory->Read<uintptr_t>(m_entityList + (i * 0x78));
        if (controller == 0 || controller == m_localController)
            continue;

        // Verificar se é um controller válido
        if (controller > 0x10000 && controller < 0x7FFFFFFFFFFF)
        {
            PlayerInfo player;
            if (ReadPlayerInfo(controller, player))
            {
                if (m_playerCount < 5) // Debug das primeiras 5 entidades
                {
                    printf("[EntityManager] Controller[%d]: 0x%p, Health=%d, Team=%d, Valid=%s\n",
                           i, (void*)controller, player.health, player.team, player.valid ? "Yes" : "No");
                }

                if (IsValidPlayer(player))
                {
                    // Calcular distância do jogador local
                    if (m_localPlayer.valid)
                    {
                        player.distance = CalculateDistance(m_localPlayer.position, player.position);
                    }

                    m_players[m_playerCount++] = player;
                    printf("[EntityManager] JOGADOR ENCONTRADO[%d]: Health=%d, Team=%d, Dist=%.1f\n",
                           m_playerCount, player.health, player.team, player.distance);

                    if (m_playerCount >= 63) // Máximo 63 outros jogadores
                        break;
                }
            }
        }
    }

    // Método 2: Se não encontrou ninguém, buscar pawns diretamente
    if (m_playerCount == 0)
    {
        printf("[EntityManager] Método 1 falhou, tentando busca direta de pawns...\n");

        for (int i = 1; i <= 64; i++)
        {
            uintptr_t entity = m_memory->Read<uintptr_t>(m_entityList + (i * 0x78));
            if (entity == 0 || entity == m_localPawn)
                continue;

            // Verificar se é um pawn válido
            if (entity > 0x10000 && entity < 0x7FFFFFFFFFFF)
            {
                int health = m_memory->Read<int>(entity + offsets.m_iHealth);
                int team = m_memory->Read<int>(entity + offsets.m_iTeamNum_Pawn);

                if (m_playerCount < 5) // Debug
                {
                    printf("[EntityManager] Entity[%d]: 0x%p, Health=%d, Team=%d\n",
                           i, (void*)entity, health, team);
                }

                if ((health > 0 && health <= 100) && (team == 2 || team == 3))
                {
                    PlayerInfo player;
                    player.controller = 0; // Desconhecido
                    player.pawn = entity;
                    player.health = health;
                    player.team = team;
                    player.alive = true;
                    player.valid = true;
                    player.position = m_memory->Read<Vector3>(entity + offsets.m_vOldOrigin);
                    strcpy_s(player.name, sizeof(player.name), "Unknown");

                    if (m_localPlayer.valid)
                    {
                        player.distance = CalculateDistance(m_localPlayer.position, player.position);
                    }

                    m_players[m_playerCount++] = player;
                    printf("[EntityManager] PAWN ENCONTRADO[%d]: Health=%d, Team=%d, Dist=%.1f\n",
                           m_playerCount, player.health, player.team, player.distance);

                    if (m_playerCount >= 63)
                        break;
                }
            }
        }
    }

    printf("[EntityManager] === RESULTADO DA BUSCA ===\n");
    printf("[EntityManager] Jogadores encontrados: %d\n", m_playerCount);
    printf("[EntityManager] ========================\n");

    return m_playerCount > 0;
}

bool EntityManager::ReadPlayerInfo(uintptr_t controller, PlayerInfo& player)
{
    player.controller = controller;
    player.health = 0;
    player.team = 0;
    player.alive = false;
    player.valid = false;
    player.pawn = 0;

    // Ler dados do controller
    if (offsets.m_iPawnHealth != 0)
        player.health = m_memory->Read<int>(controller + offsets.m_iPawnHealth);

    if (offsets.m_bPawnIsAlive != 0)
        player.alive = m_memory->Read<bool>(controller + offsets.m_bPawnIsAlive);

    // Tentar resolver pawn
    player.pawn = ResolvePawnFromController(controller);

    if (player.pawn != 0)
    {
        // Ler dados do pawn
        if (offsets.m_iHealth != 0)
        {
            int pawnHealth = m_memory->Read<int>(player.pawn + offsets.m_iHealth);
            if (pawnHealth > 0 && pawnHealth <= 100)
                player.health = pawnHealth; // Preferir health do pawn
        }

        if (offsets.m_iTeamNum_Pawn != 0)
            player.team = m_memory->Read<int>(player.pawn + offsets.m_iTeamNum_Pawn);

        if (offsets.m_vOldOrigin != 0)
            player.position = m_memory->Read<Vector3>(player.pawn + offsets.m_vOldOrigin);
    }

    // Se não conseguiu resolver pawn, tentar ler team do controller
    if (player.team == 0)
    {
        // Tentar diferentes offsets para team no controller
        uintptr_t teamOffsets[] = { 0x3E3, 0x3E0, 0x3E4, 0x3E8, 0x3DC };
        for (int i = 0; i < 5; i++)
        {
            int testTeam = m_memory->Read<int>(controller + teamOffsets[i]);
            if (testTeam == 2 || testTeam == 3)
            {
                player.team = testTeam;
                break;
            }
        }
    }

    // Ler nome do jogador
    ReadPlayerName(controller, player.name, sizeof(player.name));

    // Validação mais flexível
    player.valid = (player.health > 0 && player.health <= 100) ||  // Health válido OU
                   (player.team == 2 || player.team == 3) ||        // Team válido OU
                   player.alive;                                    // Está vivo

    return player.valid;
}

uintptr_t EntityManager::ResolvePawnFromController(uintptr_t controller)
{
    if (offsets.m_hPlayerPawn == 0)
        return 0;
    
    uintptr_t handle = m_memory->Read<uintptr_t>(controller + offsets.m_hPlayerPawn);
    if (handle == 0 || handle == 0xFFFFFFFF)
        return 0;
    
    // Método 1: Handle padrão
    uintptr_t index = handle & 0x7FFFFFFF;
    if (index > 0 && index < 2048)
    {
        uintptr_t pawn = m_memory->Read<uintptr_t>(m_entityList + 0x10 + (index * 0x78));
        if (pawn != 0)
            return pawn;
    }
    
    // Método 2: Busca direta na entity list
    for (int i = 1; i <= 64; i++)
    {
        uintptr_t entity = m_memory->Read<uintptr_t>(m_entityList + (i * 0x78));
        if (entity != 0 && offsets.m_iHealth != 0)
        {
            int health = m_memory->Read<int>(entity + offsets.m_iHealth);
            int team = m_memory->Read<int>(entity + offsets.m_iTeamNum_Pawn);
            
            if ((health > 0 && health <= 100) && (team == 2 || team == 3))
            {
                return entity;
            }
        }
    }
    
    return 0;
}

Vector3 EntityManager::GetPlayerPosition(uintptr_t pawn)
{
    if (pawn == 0 || offsets.m_vOldOrigin == 0)
        return Vector3();
    
    return m_memory->Read<Vector3>(pawn + offsets.m_vOldOrigin);
}

bool EntityManager::ReadPlayerName(uintptr_t controller, char* name, size_t maxLen)
{
    // Tentar diferentes offsets para nome
    uintptr_t nameOffsets[] = { 0x770, 0x774, 0x778, 0x76C, 0x780 };
    
    for (int i = 0; i < 5; i++)
    {
        for (size_t j = 0; j < maxLen - 1; j++)
        {
            char c = m_memory->Read<char>(controller + nameOffsets[i] + j);
            if (c >= 32 && c <= 126) // Caracteres imprimíveis
            {
                name[j] = c;
            }
            else
            {
                name[j] = '\0';
                if (j > 2) // Nome com pelo menos 3 caracteres
                    return true;
                break;
            }
        }
    }
    
    strcpy_s(name, maxLen, "Unknown");
    return false;
}

bool EntityManager::IsValidPlayer(const PlayerInfo& player) const
{
    return player.valid &&
           player.controller != 0 &&
           player.health > 0 && player.health <= 100 &&
           (player.team == 2 || player.team == 3) &&
           player.alive;
}

float EntityManager::CalculateDistance(const Vector3& pos1, const Vector3& pos2) const
{
    float dx = pos1.x - pos2.x;
    float dy = pos1.y - pos2.y;
    float dz = pos1.z - pos2.z;
    return sqrtf(dx * dx + dy * dy + dz * dz);
}
