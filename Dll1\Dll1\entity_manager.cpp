#include "pch.h"
#include "entity_manager.h"
#include <functional>

EntityManager::EntityManager(Memory* memory, uintptr_t clientBase) 
    : m_memory(memory), m_entityList(0), m_localController(0), m_localPawn(0), 
      m_clientBase(clientBase), m_playerCount(0), m_offsetFinder(nullptr)
{
    m_offsetFinder = new AdvancedOffsetFinder(memory, clientBase);
}

EntityManager::~EntityManager()
{
    if (m_offsetFinder) {
        delete m_offsetFinder;
        m_offsetFinder = nullptr;
    }
}

bool EntityManager::Initialize(uintptr_t entityList, uintptr_t localController, uintptr_t localPawn)
{
    m_entityList = entityList;
    m_localController = localController;
    m_localPawn = localPawn;
    
    printf("[EntityManager] === INICIALIZAÇÃO PROFISSIONAL ===\n");
    printf("[EntityManager] EntityList: 0x%p\n", (void*)entityList);
    printf("[EntityManager] LocalController: 0x%p\n", (void*)localController);
    printf("[EntityManager] LocalPawn: 0x%p\n", (void*)localPawn);
    
    // Usar sistema avançado de detecção de offsets
    if (!m_offsetFinder || !m_offsetFinder->Initialize(entityList, localController, localPawn))
    {
        printf("[EntityManager] ❌ FALHA CRÍTICA: Não foi possível encontrar offsets!\n");
        return false;
    }
    
    // Copiar offsets encontrados
    m_offsets = m_offsetFinder->GetOffsets();
    
    printf("[EntityManager] ✅ SISTEMA PROFISSIONAL INICIALIZADO!\n");
    printf("[EntityManager] =====================================\n");
    return true;
}

bool EntityManager::Update()
{
    if (!m_offsets.valid)
        return false;
    
    // Atualizar jogador local
    if (!UpdateLocalPlayer())
    {
        printf("[EntityManager] ⚠️ Falha ao atualizar jogador local\n");
        return false;
    }
    
    // Atualizar outros jogadores
    UpdatePlayers();
    
    return true;
}

bool EntityManager::UpdateLocalPlayer()
{
    if (m_localController == 0 || m_localPawn == 0)
        return false;
    
    m_localPlayer.controller = m_localController;
    m_localPlayer.pawn = m_localPawn;
    
    // Ler dados do pawn local usando offsets corretos
    if (m_offsets.m_iHealth != 0)
        m_localPlayer.health = m_memory->Read<int>(m_localPawn + m_offsets.m_iHealth);
    
    if (m_offsets.m_iTeamNum_Pawn != 0)
        m_localPlayer.team = m_memory->Read<int>(m_localPawn + m_offsets.m_iTeamNum_Pawn);
    
    if (m_offsets.m_bPawnIsAlive != 0)
        m_localPlayer.alive = m_memory->Read<bool>(m_localController + m_offsets.m_bPawnIsAlive);
    
    // Usar offset de posição correto
    if (m_offsets.m_vecOrigin != 0)
        m_localPlayer.position = m_memory->Read<Vector3>(m_localPawn + m_offsets.m_vecOrigin);
    else if (m_offsets.m_vecAbsOrigin != 0)
        m_localPlayer.position = m_memory->Read<Vector3>(m_localPawn + m_offsets.m_vecAbsOrigin);
    
    // Nome do jogador local
    if (m_offsets.m_sSanitizedPlayerName != 0) {
        ReadPlayerName(m_localController, m_localPlayer.name, sizeof(m_localPlayer.name));
    } else {
        strcpy_s(m_localPlayer.name, sizeof(m_localPlayer.name), "LocalPlayer");
    }
    
    m_localPlayer.valid = (m_localPlayer.health > 0 && m_localPlayer.health <= 100) &&
                         (m_localPlayer.team == 2 || m_localPlayer.team == 3);
    
    printf("[EntityManager] 🎮 JOGADOR LOCAL ANALISADO:\n");
    printf("[EntityManager]   Nome: %s\n", m_localPlayer.name);
    printf("[EntityManager]   Health: %d\n", m_localPlayer.health);
    printf("[EntityManager]   Team: %d (%s)\n", m_localPlayer.team, 
           m_localPlayer.team == 2 ? "TERRORISTA" : "COUNTER-TERRORIST");
    printf("[EntityManager]   Position: (%.1f, %.1f, %.1f)\n", 
           m_localPlayer.position.x, m_localPlayer.position.y, m_localPlayer.position.z);
    printf("[EntityManager]   Status: %s\n", m_localPlayer.valid ? "✅ VÁLIDO" : "❌ INVÁLIDO");
    
    return m_localPlayer.valid;
}

bool EntityManager::UpdatePlayers()
{
    m_playerCount = 0;
    
    printf("[EntityManager] === 🔍 ANÁLISE PROFISSIONAL DE JOGADORES ===\n");
    
    // Método profissional: Análise completa da EntityList
    for (int i = 1; i <= 64; i++)
    {
        uintptr_t controller = m_memory->Read<uintptr_t>(m_entityList + (i * m_offsets.m_ListEntry));
        if (controller == 0 || controller == m_localController)
            continue;
        
        // Verificar se é um controller válido
        if (controller > 0x10000 && controller < 0x7FFFFFFFFFFF)
        {
            PlayerInfo player;
            if (AnalyzePlayer(controller, player))
            {
                if (ValidatePlayerData(player))
                {
                    // Calcular distância do jogador local
                    if (m_localPlayer.valid)
                    {
                        player.distance = CalculateDistance(m_localPlayer.position, player.position);
                    }
                    
                    m_players[m_playerCount] = player;
                    PrintPlayerAnalysis(player, m_playerCount + 1);
                    m_playerCount++;
                    
                    if (m_playerCount >= 63) // Máximo 63 outros jogadores
                        break;
                }
            }
        }
    }
    
    printf("[EntityManager] === RESULTADO DA BUSCA ===\n");
    printf("[EntityManager] Jogadores encontrados: %d\n", m_playerCount);
    printf("[EntityManager] ========================\n");
    
    return m_playerCount > 0;
}

uintptr_t EntityManager::ResolvePawnFromController(uintptr_t controller)
{
    if (controller == 0)
        return 0;
    
    // Método 1: Usar handle se disponível
    if (m_offsets.m_hPlayerPawn != 0)
    {
        uintptr_t handle = m_memory->Read<uintptr_t>(controller + m_offsets.m_hPlayerPawn);
        if (handle != 0 && handle != 0xFFFFFFFF)
        {
            uintptr_t index = handle & 0x7FFFFFFF;
            if (index > 0 && index < 2048)
            {
                uintptr_t pawn = m_memory->Read<uintptr_t>(m_entityList + 0x10 + (index * m_offsets.m_ListEntry));
                if (pawn > 0x10000 && pawn < 0x7FFFFFFFFFFF)
                    return pawn;
            }
        }
    }
    
    // Método 2: Buscar pawn por correlação de dados
    for (int i = 1; i <= 64; i++)
    {
        uintptr_t entity = m_memory->Read<uintptr_t>(m_entityList + (i * m_offsets.m_ListEntry));
        if (entity == 0 || entity == m_localPawn)
            continue;
        
        if (entity > 0x10000 && entity < 0x7FFFFFFFFFFF)
        {
            // Verificar se este entity tem dados consistentes
            if (m_offsets.m_iHealth != 0)
            {
                int health = m_memory->Read<int>(entity + m_offsets.m_iHealth);
                if (health > 0 && health <= 100)
                {
                    return entity;
                }
            }
        }
    }
    
    return 0;
}

Vector3 EntityManager::GetPlayerPosition(uintptr_t pawn)
{
    if (pawn == 0)
        return Vector3();
    
    if (m_offsets.m_vecOrigin != 0)
        return m_memory->Read<Vector3>(pawn + m_offsets.m_vecOrigin);
    else if (m_offsets.m_vecAbsOrigin != 0)
        return m_memory->Read<Vector3>(pawn + m_offsets.m_vecAbsOrigin);
    
    return Vector3();
}

bool EntityManager::ReadPlayerName(uintptr_t controller, char* name, size_t maxLen)
{
    if (controller == 0 || name == nullptr || maxLen == 0)
        return false;
    
    if (m_offsets.m_sSanitizedPlayerName != 0)
    {
        for (size_t i = 0; i < maxLen - 1; i++)
        {
            char c = m_memory->Read<char>(controller + m_offsets.m_sSanitizedPlayerName + i);
            if (c == 0) break;
            name[i] = c;
            name[i + 1] = 0;
        }
        
        if (strlen(name) > 2)
            return true;
    }
    
    strcpy_s(name, maxLen, "Unknown");
    return false;
}

bool EntityManager::IsValidPlayer(const PlayerInfo& player) const
{
    return player.valid &&
           player.controller != 0 &&
           player.health > 0 && player.health <= 100 &&
           (player.team == 2 || player.team == 3) &&
           player.alive;
}

float EntityManager::CalculateDistance(const Vector3& pos1, const Vector3& pos2) const
{
    float dx = pos1.x - pos2.x;
    float dy = pos1.y - pos2.y;
    float dz = pos1.z - pos2.z;
    return sqrtf(dx * dx + dy * dy + dz * dz);
}

bool EntityManager::AnalyzePlayer(uintptr_t controller, PlayerInfo& player)
{
    // Inicializar estrutura
    memset(&player, 0, sizeof(PlayerInfo));
    player.controller = controller;

    // Ler dados básicos do controller
    if (m_offsets.m_iPawnHealth != 0)
        player.health = m_memory->Read<int>(controller + m_offsets.m_iPawnHealth);

    if (m_offsets.m_bPawnIsAlive != 0)
        player.alive = m_memory->Read<bool>(controller + m_offsets.m_bPawnIsAlive);

    // Resolver pawn usando método profissional
    player.pawn = ResolvePawnFromController(controller);

    if (player.pawn != 0 && player.pawn > 0x10000 && player.pawn < 0x7FFFFFFFFFFF)
    {
        // Ler dados do pawn com offsets corretos
        if (m_offsets.m_iHealth != 0)
        {
            int pawnHealth = m_memory->Read<int>(player.pawn + m_offsets.m_iHealth);
            if (pawnHealth > 0 && pawnHealth <= 100)
                player.health = pawnHealth; // Preferir health do pawn
        }

        if (m_offsets.m_iTeamNum_Pawn != 0)
            player.team = m_memory->Read<int>(player.pawn + m_offsets.m_iTeamNum_Pawn);

        // Ler posição com offset correto
        if (m_offsets.m_vecOrigin != 0)
            player.position = m_memory->Read<Vector3>(player.pawn + m_offsets.m_vecOrigin);
        else if (m_offsets.m_vecAbsOrigin != 0)
            player.position = m_memory->Read<Vector3>(player.pawn + m_offsets.m_vecAbsOrigin);
    }

    // Ler nome do jogador
    if (m_offsets.m_sSanitizedPlayerName != 0)
    {
        ReadPlayerName(controller, player.name, sizeof(player.name));
    }
    else
    {
        sprintf_s(player.name, sizeof(player.name), "Player_%d", (int)(controller & 0xFFFF));
    }

    player.valid = true;
    return true;
}

bool EntityManager::ValidatePlayerData(const PlayerInfo& player)
{
    // Validação rigorosa de dados
    bool hasValidHealth = (player.health > 0 && player.health <= 100);
    bool hasValidTeam = (player.team == 2 || player.team == 3);
    bool hasValidPosition = (player.position.x != 0.0f || player.position.y != 0.0f || player.position.z != 0.0f) &&
                           (abs(player.position.x) < 50000 && abs(player.position.y) < 50000 && abs(player.position.z) < 10000);
    bool hasValidPawn = (player.pawn > 0x10000 && player.pawn < 0x7FFFFFFFFFFF);

    // Filtrar jogador local (distância muito pequena)
    if (m_localPlayer.valid)
    {
        float dist = CalculateDistance(m_localPlayer.position, player.position);
        if (dist < 5.0f) // Muito próximo, provavelmente é o próprio jogador
            return false;
    }

    // Pelo menos 2 dos 4 critérios devem ser válidos
    int validCriteria = 0;
    if (hasValidHealth) validCriteria++;
    if (hasValidTeam) validCriteria++;
    if (hasValidPosition) validCriteria++;
    if (hasValidPawn) validCriteria++;

    return (validCriteria >= 2);
}

void EntityManager::PrintPlayerAnalysis(const PlayerInfo& player, int index)
{
    printf("[EntityManager] 🎯 JOGADOR DETECTADO[%d]:\n", index);
    printf("[EntityManager]   📛 Nome: %s\n", player.name);
    printf("[EntityManager]   🏥 Health: %d\n", player.health);

    if (player.team == 2)
        printf("[EntityManager]   👤 Team: TERRORISTA\n");
    else if (player.team == 3)
        printf("[EntityManager]   👤 Team: COUNTER-TERRORIST\n");
    else
        printf("[EntityManager]   👤 Team: DESCONHECIDO (%d)\n", player.team);

    printf("[EntityManager]   📍 Position: (%.1f, %.1f, %.1f)\n",
           player.position.x, player.position.y, player.position.z);
    printf("[EntityManager]   📏 Distância: %.1f unidades\n", player.distance);
    printf("[EntityManager]   💻 Pawn: 0x%p\n", (void*)player.pawn);
    printf("[EntityManager]   ✅ Status: VÁLIDO\n");
    printf("[EntityManager]\n");
}
