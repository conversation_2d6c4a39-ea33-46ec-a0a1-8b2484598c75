#pragma once
#include "pch.h"
#include <vector>

// Pattern scanner simples para encontrar offsets dinamicamente
class PatternScanner
{
private:
    uintptr_t m_moduleBase;
    size_t m_moduleSize;
    
public:
    PatternScanner(uintptr_t moduleBase);
    ~PatternScanner();
    
    bool Initialize();
    uintptr_t FindPattern(const char* pattern, const char* mask);
    uintptr_t FindPatternIDA(const char* pattern); // Formato IDA: "AA BB ? CC DD"
    
    // Patterns específicos para CS2
    uintptr_t FindEntityList();
    uintptr_t FindLocalPlayerController();
    uintptr_t FindLocalPlayerPawn();
    uintptr_t FindViewMatrix();
    uintptr_t FindGlobalVars();
    uintptr_t FindEntitySystem();
    
private:
    bool CompareBytes(const uint8_t* data, const uint8_t* pattern, const char* mask);
    std::vector<uint8_t> ParseIDAPattern(const char* pattern);
    size_t GetModuleSize(uintptr_t moduleBase);
};
