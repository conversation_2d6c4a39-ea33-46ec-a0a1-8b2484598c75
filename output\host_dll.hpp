// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-06 15:56:26.663360300 UTC

#pragma once

#include <cstddef>

namespace cs2_dumper {
    namespace schemas {
        // Module: host.dll
        // Class count: 2
        // Enum count: 0
        namespace host_dll {
            // Parent: CAnimScriptBase
            // Field count: 1
            namespace EmptyTestScript {
                constexpr std::ptrdiff_t m_hTest = 0x10; // CAnimScriptParam<float32>
            }
            // Parent: None
            // Field count: 1
            namespace CAnimScriptBase {
                constexpr std::ptrdiff_t m_bIsValid = 0x8; // bool
            }
        }
    }
}
