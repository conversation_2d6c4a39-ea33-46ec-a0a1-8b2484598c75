#include "pch.h"
#include "esp.h"

ESP::ESP() : m_pM<PERSON>ory(nullptr), m_pR<PERSON>(nullptr), m_pMenu(nullptr), m_dwClient(0), m_dwEngine(0)
{
}

ESP::~ESP()
{
    Shutdown();
}

bool ESP::Initialize()
{
    printf("[ESP] Inicializando ESP...\n");
    
    // Inicializar sistema de memória
    m_pMemory = new Memory();
    if (!m_pMemory->Initialize())
    {
        printf("[ESP] Falha ao inicializar sistema de memória!\n");
        return false;
    }
    
    // Inicializar sistema de renderização
    m_pRender = new Render();
    if (!m_pRender->Initialize())
    {
        printf("[ESP] Falha ao inicializar sistema de renderização!\n");
        return false;
    }

    // Inicializar menu
    m_pMenu = new Menu();
    if (!m_pMenu->Initialize())
    {
        printf("[ESP] Falha ao inicializar menu!\n");
        return false;
    }
    
    // Inicializar módulos e offsets
    if (!InitializeModules() || !InitializeOffsets())
    {
        printf("[ESP] Falha ao inicializar módulos/offsets!\n");
        return false;
    }
    
    printf("[ESP] ESP inicializado com sucesso!\n");
    return true;
}

void ESP::Shutdown()
{
    if (m_pMenu)
    {
        m_pMenu->Shutdown();
        delete m_pMenu;
        m_pMenu = nullptr;
    }

    if (m_pRender)
    {
        m_pRender->Shutdown();
        delete m_pRender;
        m_pRender = nullptr;
    }

    if (m_pMemory)
    {
        m_pMemory->Shutdown();
        delete m_pMemory;
        m_pMemory = nullptr;
    }
}

void ESP::Update()
{
    if (!m_pMemory || !m_pRender || !m_pRender->IsInitialized())
        return;

    // Processar input
    HandleInput();

    // Atualizar jogador local
    UpdateLocalPlayer();

    // Atualizar entidades
    UpdateEntities();

    // Renderizar ESP
    RenderESP();

    // Renderizar menu
    if (m_pMenu)
    {
        m_pMenu->Render();
    }
}

bool ESP::InitializeModules()
{
    m_dwClient = m_pMemory->GetModuleBase(Offsets::Modules::CLIENT_DLL);
    if (!m_dwClient)
    {
        printf("[ESP] Falha ao obter client.dll!\n");
        return false;
    }
    
    m_dwEngine = m_pMemory->GetModuleBase(Offsets::Modules::ENGINE2_DLL);
    if (!m_dwEngine)
    {
        printf("[ESP] Falha ao obter engine2.dll!\n");
        return false;
    }
    
    return true;
}

bool ESP::InitializeOffsets()
{
    m_dwEntityList = m_dwClient + Offsets::Client::dwEntityList;
    m_dwLocalPlayerController = m_dwClient + Offsets::Client::dwLocalPlayerController;
    m_dwLocalPlayerPawn = m_dwClient + Offsets::Client::dwLocalPlayerPawn;
    m_dwViewMatrix = m_dwClient + Offsets::Client::dwViewMatrix;
    
    printf("[ESP] Offsets inicializados:\n");
    printf("  EntityList: 0x%p\n", (void*)m_dwEntityList);
    printf("  LocalPlayerController: 0x%p\n", (void*)m_dwLocalPlayerController);
    printf("  LocalPlayerPawn: 0x%p\n", (void*)m_dwLocalPlayerPawn);
    printf("  ViewMatrix: 0x%p\n", (void*)m_dwViewMatrix);
    
    return true;
}

void ESP::UpdateLocalPlayer()
{
    // Obter controller do jogador local
    uintptr_t localController = m_pMemory->Read<uintptr_t>(m_dwLocalPlayerController);
    if (!localController || !m_pMemory->IsValidAddress(localController))
        return;
    
    // Obter pawn do jogador local
    uintptr_t localPawn = m_pMemory->Read<uintptr_t>(m_dwLocalPlayerPawn);
    if (!localPawn || !m_pMemory->IsValidAddress(localPawn))
        return;
    
    // Atualizar entidade do jogador local
    m_LocalPlayer = Entity(m_pMemory, localPawn);
    m_LocalPlayer.SetController(localController);
}

void ESP::UpdateEntities()
{
    m_Entities.clear();
    
    // Obter entity list
    uintptr_t entityList = m_pMemory->Read<uintptr_t>(m_dwEntityList);
    if (!entityList || !m_pMemory->IsValidAddress(entityList))
        return;
    
    // Iterar através das entidades (máximo 64 jogadores)
    for (int i = 1; i <= 64; i++)
    {
        // Obter controller
        uintptr_t controller = m_pMemory->Read<uintptr_t>(entityList + (i * 0x78));
        if (!controller || !m_pMemory->IsValidAddress(controller))
            continue;
        
        // Obter pawn do controller
        uintptr_t pawnHandle = m_pMemory->Read<uintptr_t>(controller + Offsets::Entity::m_hPlayerPawn);
        if (!pawnHandle)
            continue;
        
        // Converter handle para endereço
        uintptr_t pawn = m_pMemory->Read<uintptr_t>(entityList + 0x10 + ((pawnHandle & 0x7FFFFFFF) * 0x78));
        if (!pawn || !m_pMemory->IsValidAddress(pawn))
            continue;
        
        // Criar entidade
        Entity entity(m_pMemory, pawn);
        entity.SetController(controller);
        
        // Verificar se é válida
        if (IsValidEntity(entity))
        {
            m_Entities.push_back(entity);
        }
    }
    
    printf("[ESP] Entidades encontradas: %d\n", (int)m_Entities.size());
}

void ESP::RenderESP()
{
    if (!m_Config.bEnabled || !m_pRender->IsInitialized())
        return;
    
    // Obter view matrix
    ViewMatrix viewMatrix = m_pMemory->Read<ViewMatrix>(m_dwViewMatrix);
    
    // Renderizar cada entidade
    for (const auto& entity : m_Entities)
    {
        if (!entity.IsValid() || !entity.IsAlive())
            continue;
        
        // Verificar distância
        if (m_LocalPlayer.IsValid())
        {
            float distance = entity.GetDistanceFrom(m_LocalPlayer);
            if (distance > m_Config.fMaxDistance)
                continue;
        }
        
        // Obter posições
        Vector3 headPos = entity.GetHeadPosition();
        Vector3 feetPos = entity.GetAbsOrigin();
        
        Vector2 headScreen, feetScreen;
        
        // Converter para coordenadas de tela
        if (!m_pMemory->WorldToScreen(headPos, headScreen, viewMatrix, m_pRender->GetScreenWidth(), m_pRender->GetScreenHeight()) ||
            !m_pMemory->WorldToScreen(feetPos, feetScreen, viewMatrix, m_pRender->GetScreenWidth(), m_pRender->GetScreenHeight()))
            continue;
        
        // Determinar cor
        bool isEnemy = entity.IsEnemy(m_LocalPlayer);
        uint32_t color = isEnemy ? 
            Render::ColorToUInt32(m_Config.colorEnemy) : 
            Render::ColorToUInt32(m_Config.colorTeam);
        
        // Desenhar box
        if (m_Config.bShowBoxes)
        {
            m_pRender->DrawPlayerBox(headScreen, feetScreen, color);
        }
        
        // Desenhar health bar
        if (m_Config.bShowHealth)
        {
            Vector2 healthPos = Vector2(feetScreen.x - 30, feetScreen.y);
            Vector2 healthSize = Vector2(5, feetScreen.y - headScreen.y);
            m_pRender->DrawHealthBar(healthPos, healthSize, entity.GetHealth(), entity.GetMaxHealth());
        }
        
        // Desenhar informações do jogador
        if (m_Config.bShowNames || m_Config.bShowDistance)
        {
            std::string info = "";
            
            if (m_Config.bShowNames)
            {
                info += entity.GetName();
            }
            
            if (m_Config.bShowDistance && m_LocalPlayer.IsValid())
            {
                if (!info.empty()) info += " ";
                int distance = (int)entity.GetDistanceFrom(m_LocalPlayer);
                info += "[" + std::to_string(distance) + "m]";
            }
            
            if (!info.empty())
            {
                Vector2 textPos = Vector2(headScreen.x, headScreen.y - 20);
                m_pRender->DrawPlayerInfo(textPos, info.c_str(), entity.GetHealth(), 0, color);
            }
        }
    }
}

bool ESP::IsValidEntity(const Entity& entity)
{
    if (!entity.IsValid() || !entity.IsPlayer())
        return false;
    
    // Verificar se está vivo
    if (!entity.IsAlive())
        return false;
    
    // Verificar se não é o jogador local
    if (m_LocalPlayer.IsValid() && entity.GetAddress() == m_LocalPlayer.GetAddress())
        return false;
    
    // Verificar team se necessário
    if (!m_Config.bShowTeam && m_LocalPlayer.IsValid())
    {
        if (!entity.IsEnemy(m_LocalPlayer))
            return false;
    }
    
    return true;
}

float ESP::GetDistance(const Vector3& pos1, const Vector3& pos2)
{
    return pos1.Distance(pos2);
}

void ESP::HandleInput()
{
    // Toggle menu com INSERT
    static bool insertPressed = false;
    if (GetAsyncKeyState(VK_INSERT) & 0x8000)
    {
        if (!insertPressed && m_pMenu)
        {
            m_pMenu->Toggle();
            insertPressed = true;
        }
    }
    else
    {
        insertPressed = false;
    }
}
