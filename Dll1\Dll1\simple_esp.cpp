#include "pch.h"
#include "simple_esp.h"

SimpleESP::SimpleESP() : m_pM<PERSON>ory(nullptr), m_pScan<PERSON>(nullptr), m_hClient(nullptr), m_hE<PERSON>ine(nullptr), m_bRunning(false)
{
}

SimpleESP::~SimpleESP()
{
    Shutdown();
}

bool SimpleESP::Initialize()
{
    printf("[SimpleESP] Inicializando ESP simplificado...\n");
    
    // Inicializar sistema de memória
    m_pMemory = new Memory();
    if (!m_pMemory || !m_pMemory->Initialize())
    {
        printf("[SimpleESP] Falha ao inicializar sistema de memória!\n");
        return false;
    }
    
    // Encontrar módulos
    if (!FindModules())
    {
        printf("[SimpleESP] Falha ao encontrar módulos!\n");
        return false;
    }
    
    // Tentar encontrar offsets com pattern scanning primeiro
    if (!FindOffsetsWithPatterns())
    {
        printf("[SimpleESP] Pattern scanning falhou, usando offsets fixos...\n");
        if (!FindOffsets())
        {
            printf("[SimpleESP] Falha ao encontrar offsets!\n");
            return false;
        }
    }
    
    m_bRunning = true;
    printf("[SimpleESP] ESP simplificado inicializado com sucesso!\n");
    return true;
}

void SimpleESP::Shutdown()
{
    m_bRunning = false;

    if (m_pScanner)
    {
        delete m_pScanner;
        m_pScanner = nullptr;
    }

    if (m_pMemory)
    {
        m_pMemory->Shutdown();
        delete m_pMemory;
        m_pMemory = nullptr;
    }
}

void SimpleESP::Run()
{
    printf("[SimpleESP] Iniciando loop principal...\n");
    printf("[SimpleESP] Pressione END para sair\n");
    
    int frameCounter = 0;
    
    while (m_bRunning && !GetAsyncKeyState(VK_END))
    {
        try
        {
            // Atualizar entidades a cada 60 frames (~1 segundo)
            if (frameCounter % 60 == 0)
            {
                UpdateEntities();
            }
            
            // Debug a cada 300 frames (~5 segundos)
            if (frameCounter % 300 == 0)
            {
                printf("[SimpleESP] ESP rodando... Frame: %d\n", frameCounter);
            }
            
            frameCounter++;
            Sleep(16); // ~60 FPS
        }
        catch (...)
        {
            printf("[SimpleESP] Erro no loop principal!\n");
            break;
        }
    }
    
    printf("[SimpleESP] Loop principal finalizado\n");
}

bool SimpleESP::FindModules()
{
    m_hClient = GetModuleHandleA("client.dll");
    m_hEngine = GetModuleHandleA("engine2.dll");
    
    printf("[SimpleESP] client.dll: 0x%p\n", m_hClient);
    printf("[SimpleESP] engine2.dll: 0x%p\n", m_hEngine);
    
    return (m_hClient != nullptr && m_hEngine != nullptr);
}

bool SimpleESP::FindOffsetsWithPatterns()
{
    printf("[SimpleESP] === PATTERN SCANNING ===\n");

    // Inicializar pattern scanner
    m_pScanner = new PatternScanner((uintptr_t)m_hClient);
    if (!m_pScanner->Initialize())
    {
        delete m_pScanner;
        m_pScanner = nullptr;
        return false;
    }

    // Encontrar offsets dinamicamente
    uintptr_t entityList = m_pScanner->FindEntityList();
    uintptr_t localController = m_pScanner->FindLocalPlayerController();
    uintptr_t localPawn = m_pScanner->FindLocalPlayerPawn();
    uintptr_t viewMatrix = m_pScanner->FindViewMatrix();

    // Verificar se encontrou pelo menos EntityList
    if (entityList == 0)
    {
        printf("[SimpleESP] Pattern scanning falhou - EntityList não encontrado!\n");
        return false;
    }

    // Usar os offsets encontrados
    m_dwEntityList = entityList;
    m_dwLocalPlayerController = localController ? localController : ((uintptr_t)m_hClient + 0x1A52D00);
    m_dwLocalPlayerPawn = localPawn ? localPawn : ((uintptr_t)m_hClient + 0x18580D0);
    m_dwViewMatrix = viewMatrix ? viewMatrix : ((uintptr_t)m_hClient + 0x1A6D260);

    printf("[SimpleESP] === OFFSETS ENCONTRADOS ===\n");
    printf("  EntityList: 0x%p %s\n", (void*)m_dwEntityList, entityList ? "(PATTERN)" : "(FIXO)");
    printf("  LocalController: 0x%p %s\n", (void*)m_dwLocalPlayerController, localController ? "(PATTERN)" : "(FIXO)");
    printf("  LocalPawn: 0x%p %s\n", (void*)m_dwLocalPlayerPawn, localPawn ? "(PATTERN)" : "(FIXO)");
    printf("  ViewMatrix: 0x%p %s\n", (void*)m_dwViewMatrix, viewMatrix ? "(PATTERN)" : "(FIXO)");
    printf("[SimpleESP] ========================\n");

    return true;
}

bool SimpleESP::FindOffsets()
{
    uintptr_t clientBase = (uintptr_t)m_hClient;

    // Offsets básicos (fallback)
    m_dwEntityList = clientBase + 0x1A044C0;
    m_dwLocalPlayerController = clientBase + 0x1A52D00;
    m_dwLocalPlayerPawn = clientBase + 0x18580D0;
    m_dwViewMatrix = clientBase + 0x1A6D260;

    printf("[SimpleESP] Usando offsets fixos (fallback):\n");
    printf("  EntityList: 0x%p\n", (void*)m_dwEntityList);
    printf("  LocalController: 0x%p\n", (void*)m_dwLocalPlayerController);
    printf("  LocalPawn: 0x%p\n", (void*)m_dwLocalPlayerPawn);
    printf("  ViewMatrix: 0x%p\n", (void*)m_dwViewMatrix);

    return true;
}

void SimpleESP::UpdateEntities()
{
    if (!m_pMemory)
        return;

    // Verificar se está em jogo
    uintptr_t localController = m_pMemory->Read<uintptr_t>(m_dwLocalPlayerController);
    uintptr_t localPawn = m_pMemory->Read<uintptr_t>(m_dwLocalPlayerPawn);

    bool inGame = (localController != 0 && localPawn != 0);

    printf("[SimpleESP] Status: %s | LocalController: 0x%p, LocalPawn: 0x%p\n",
           inGame ? "EM JOGO" : "FORA DO JOGO", (void*)localController, (void*)localPawn);

    if (!inGame)
    {
        printf("[SimpleESP] Nao esta em uma partida! Entre em um servidor/mapa.\n");
        return;
    }

    // Ler entity list
    uintptr_t entityList = m_pMemory->Read<uintptr_t>(m_dwEntityList);
    if (!IsValidPointer(entityList))
    {
        printf("[SimpleESP] EntityList inválido: 0x%p\n", (void*)entityList);
        return;
    }

    // Contar entidades válidas e jogadores reais
    int validControllers = 0;
    int realPlayers = 0;

    printf("[SimpleESP] === ANALISE DE ENTIDADES ===\n");

    for (int i = 1; i <= 64; i++)
    {
        uintptr_t controller = m_pMemory->Read<uintptr_t>(entityList + (i * 0x78));
        if (!IsValidPointer(controller))
            continue;

        validControllers++;

        // Ler dados do controller
        uintptr_t pawnHandle = m_pMemory->Read<uintptr_t>(controller + 0x824); // m_hPlayerPawn
        int controllerHealth = m_pMemory->Read<int>(controller + 0x830);       // m_iPawnHealth
        bool isAlive = m_pMemory->Read<bool>(controller + 0x82C);              // m_bPawnIsAlive

        // Tentar diferentes métodos para resolver o pawn
        uintptr_t pawn = 0;

        // Método 1: Handle padrão
        if (pawnHandle != 0)
        {
            uintptr_t pawnIndex = pawnHandle & 0x7FFFFFFF;
            if (pawnIndex > 0 && pawnIndex < 2048) // Range válido
            {
                pawn = m_pMemory->Read<uintptr_t>(entityList + 0x10 + (pawnIndex * 0x78));
            }
        }

        // Método 2: Se não funcionou, tentar handle direto
        if (!IsValidPointer(pawn) && pawnHandle != 0)
        {
            pawn = pawnHandle;
        }

        // Verificar se é um jogador real
        bool isRealPlayer = false;
        int pawnHealth = 0;
        int pawnTeam = 0;

        if (IsValidPointer(pawn))
        {
            pawnHealth = m_pMemory->Read<int>(pawn + 0x344); // m_iHealth
            pawnTeam = m_pMemory->Read<int>(pawn + 0x3E3);   // m_iTeamNum

            // Validar se é jogador real
            isRealPlayer = (pawnHealth > 0 && pawnHealth <= 100) &&
                          (pawnTeam == 2 || pawnTeam == 3) && // CT ou T
                          isAlive;
        }

        if (isRealPlayer)
        {
            realPlayers++;
            printf("[SimpleESP] JOGADOR[%d]:\n", realPlayers);
            printf("  Controller: 0x%p\n", (void*)controller);
            printf("  PawnHandle: 0x%X -> Pawn: 0x%p\n", pawnHandle, (void*)pawn);
            printf("  Health: %d (Controller: %d)\n", pawnHealth, controllerHealth);
            printf("  Team: %d, Alive: %s\n", pawnTeam, isAlive ? "Yes" : "No");
            printf("\n");
        }
        else if (validControllers <= 10) // Debug das primeiras 10 entidades
        {
            printf("[SimpleESP] Entity[%d]: Controller=0x%p, Handle=0x%X\n",
                   i, (void*)controller, pawnHandle);
            printf("  Health: %d/%d, Team: %d, Alive: %s, Pawn: 0x%p\n",
                   pawnHealth, controllerHealth, pawnTeam, isAlive ? "Yes" : "No", (void*)pawn);
        }
    }

    printf("[SimpleESP] === RESULTADO ===\n");
    printf("[SimpleESP] Controllers válidos: %d\n", validControllers);
    printf("[SimpleESP] Jogadores reais: %d\n", realPlayers);
    printf("[SimpleESP] ==================\n");
}

void SimpleESP::PrintEntityInfo()
{
    // Implementar se necessário
}

bool SimpleESP::IsValidPointer(uintptr_t ptr)
{
    if (ptr == 0)
        return false;
    
    // Verificação básica de range de memória
    if (ptr < 0x10000 || ptr > 0x7FFFFFFFFFFF)
        return false;
    
    return m_pMemory && m_pMemory->IsValidAddress(ptr);
}
