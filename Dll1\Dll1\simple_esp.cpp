#include "pch.h"
#include "simple_esp.h"

SimpleESP::SimpleESP() : m_pM<PERSON><PERSON>(nullptr), m_p<PERSON><PERSON><PERSON>(nullptr), m_pEntityManager(nullptr), m_hC<PERSON>(nullptr), m_h<PERSON><PERSON><PERSON>(nullptr), m_bRunning(false)
{
}

SimpleESP::~SimpleESP()
{
    Shutdown();
}

bool SimpleESP::Initialize()
{
    printf("[SimpleESP] Inicializando ESP simplificado...\n");
    
    // Inicializar sistema de memória
    m_pMemory = new Memory();
    if (!m_pMemory || !m_pMemory->Initialize())
    {
        printf("[SimpleESP] Falha ao inicializar sistema de memória!\n");
        return false;
    }
    
    // Encontrar módulos
    if (!FindModules())
    {
        printf("[SimpleESP] Falha ao encontrar módulos!\n");
        return false;
    }
    
    // Tentar encontrar offsets com pattern scanning primeiro
    if (!FindOffsetsWithPatterns())
    {
        printf("[SimpleESP] Pattern scanning falhou, usando offsets fixos...\n");
        if (!FindOffsets())
        {
            printf("[SimpleESP] Falha ao encontrar offsets!\n");
            return false;
        }
    }
    
    m_bRunning = true;
    // Inicializar EntityManager
    m_pEntityManager = new EntityManager(m_pMemory);
    if (!m_pEntityManager->Initialize(m_dwEntityList,
                                     m_pMemory->Read<uintptr_t>(m_dwLocalPlayerController),
                                     m_pMemory->Read<uintptr_t>(m_dwLocalPlayerPawn)))
    {
        printf("[SimpleESP] Falha ao inicializar EntityManager!\n");
        return false;
    }

    printf("[SimpleESP] ESP profissional inicializado com sucesso!\n");
    return true;
}

void SimpleESP::Shutdown()
{
    m_bRunning = false;

    if (m_pEntityManager)
    {
        delete m_pEntityManager;
        m_pEntityManager = nullptr;
    }

    if (m_pScanner)
    {
        delete m_pScanner;
        m_pScanner = nullptr;
    }

    if (m_pMemory)
    {
        m_pMemory->Shutdown();
        delete m_pMemory;
        m_pMemory = nullptr;
    }
}

void SimpleESP::Run()
{
    printf("[SimpleESP] Iniciando loop principal...\n");
    printf("[SimpleESP] Pressione END para sair\n");
    
    int frameCounter = 0;
    
    while (m_bRunning && !GetAsyncKeyState(VK_END))
    {
        try
        {
            // Atualizar entidades a cada 60 frames (~1 segundo)
            if (frameCounter % 60 == 0)
            {
                UpdateEntities();
            }
            
            // Debug a cada 300 frames (~5 segundos)
            if (frameCounter % 300 == 0)
            {
                printf("[SimpleESP] ESP rodando... Frame: %d\n", frameCounter);
            }
            
            frameCounter++;
            Sleep(16); // ~60 FPS
        }
        catch (...)
        {
            printf("[SimpleESP] Erro no loop principal!\n");
            break;
        }
    }
    
    printf("[SimpleESP] Loop principal finalizado\n");
}

bool SimpleESP::FindModules()
{
    m_hClient = GetModuleHandleA("client.dll");
    m_hEngine = GetModuleHandleA("engine2.dll");
    
    printf("[SimpleESP] client.dll: 0x%p\n", m_hClient);
    printf("[SimpleESP] engine2.dll: 0x%p\n", m_hEngine);
    
    return (m_hClient != nullptr && m_hEngine != nullptr);
}

bool SimpleESP::FindOffsetsWithPatterns()
{
    printf("[SimpleESP] === PATTERN SCANNING ===\n");

    // Inicializar pattern scanner
    m_pScanner = new PatternScanner((uintptr_t)m_hClient);
    if (!m_pScanner->Initialize())
    {
        delete m_pScanner;
        m_pScanner = nullptr;
        return false;
    }

    // Encontrar offsets dinamicamente
    uintptr_t entityList = m_pScanner->FindEntityList();
    uintptr_t localController = m_pScanner->FindLocalPlayerController();
    uintptr_t localPawn = m_pScanner->FindLocalPlayerPawn();
    uintptr_t viewMatrix = m_pScanner->FindViewMatrix();

    // Verificar se encontrou pelo menos EntityList
    if (entityList == 0)
    {
        printf("[SimpleESP] Pattern scanning falhou - EntityList não encontrado!\n");
        return false;
    }

    // Usar os offsets encontrados
    m_dwEntityList = entityList;
    m_dwLocalPlayerController = localController ? localController : ((uintptr_t)m_hClient + 0x1A52D00);
    m_dwLocalPlayerPawn = localPawn ? localPawn : ((uintptr_t)m_hClient + 0x18580D0);
    m_dwViewMatrix = viewMatrix ? viewMatrix : ((uintptr_t)m_hClient + 0x1A6D260);

    printf("[SimpleESP] === OFFSETS ENCONTRADOS ===\n");
    printf("  EntityList: 0x%p %s\n", (void*)m_dwEntityList, entityList ? "(PATTERN)" : "(FIXO)");
    printf("  LocalController: 0x%p %s\n", (void*)m_dwLocalPlayerController, localController ? "(PATTERN)" : "(FIXO)");
    printf("  LocalPawn: 0x%p %s\n", (void*)m_dwLocalPlayerPawn, localPawn ? "(PATTERN)" : "(FIXO)");
    printf("  ViewMatrix: 0x%p %s\n", (void*)m_dwViewMatrix, viewMatrix ? "(PATTERN)" : "(FIXO)");
    printf("[SimpleESP] ========================\n");

    return true;
}

bool SimpleESP::FindOffsets()
{
    uintptr_t clientBase = (uintptr_t)m_hClient;

    // Offsets básicos (fallback)
    m_dwEntityList = clientBase + 0x1A044C0;
    m_dwLocalPlayerController = clientBase + 0x1A52D00;
    m_dwLocalPlayerPawn = clientBase + 0x18580D0;
    m_dwViewMatrix = clientBase + 0x1A6D260;

    printf("[SimpleESP] Usando offsets fixos (fallback):\n");
    printf("  EntityList: 0x%p\n", (void*)m_dwEntityList);
    printf("  LocalController: 0x%p\n", (void*)m_dwLocalPlayerController);
    printf("  LocalPawn: 0x%p\n", (void*)m_dwLocalPlayerPawn);
    printf("  ViewMatrix: 0x%p\n", (void*)m_dwViewMatrix);

    return true;
}

void SimpleESP::UpdateEntities()
{
    if (!m_pEntityManager)
        return;

    // Verificar se está em jogo
    uintptr_t localController = m_pMemory->Read<uintptr_t>(m_dwLocalPlayerController);
    uintptr_t localPawn = m_pMemory->Read<uintptr_t>(m_dwLocalPlayerPawn);

    bool inGame = (localController != 0 && localPawn != 0);

    printf("[SimpleESP] Status: %s | LocalController: 0x%p, LocalPawn: 0x%p\n",
           inGame ? "EM JOGO" : "FORA DO JOGO", (void*)localController, (void*)localPawn);

    if (!inGame)
    {
        printf("[SimpleESP] Nao esta em uma partida! Entre em um servidor/mapa.\n");
        return;
    }

    // Atualizar EntityManager
    m_pEntityManager->Update();

    // Mostrar informações dos jogadores
    PrintPlayerInfo();
}

void SimpleESP::PrintPlayerInfo()
{
    if (!m_pEntityManager)
        return;

    const PlayerInfo& localPlayer = m_pEntityManager->GetLocalPlayer();
    const PlayerInfo* players = m_pEntityManager->GetPlayers();
    int playerCount = m_pEntityManager->GetPlayerCount();

    printf("[SimpleESP] === INFORMAÇÕES DOS JOGADORES ===\n");

    // Mostrar jogador local
    if (localPlayer.valid)
    {
        printf("[SimpleESP] JOGADOR LOCAL:\n");
        printf("  Controller: 0x%p\n", (void*)localPlayer.controller);
        printf("  Pawn: 0x%p\n", (void*)localPlayer.pawn);
        printf("  Health: %d\n", localPlayer.health);
        printf("  Team: %d (%s)\n", localPlayer.team, localPlayer.team == 2 ? "T" : "CT");
        printf("  Position: (%.1f, %.1f, %.1f)\n", localPlayer.position.x, localPlayer.position.y, localPlayer.position.z);
        printf("  Alive: %s\n", localPlayer.alive ? "Yes" : "No");
        printf("\n");
    }

    // Mostrar outros jogadores
    for (int i = 0; i < playerCount; i++)
    {
        const PlayerInfo& player = players[i];

        printf("[SimpleESP] JOGADOR[%d]:\n", i + 1);
        printf("  Nome: %s\n", player.name);
        printf("  Pawn: 0x%p\n", (void*)player.pawn);
        printf("  Health: %d\n", player.health);

        // Mostrar team com cores
        if (player.team == 2)
            printf("  Team: 2 (TERRORISTA)\n");
        else if (player.team == 3)
            printf("  Team: 3 (COUNTER-TERRORIST)\n");
        else
            printf("  Team: %d (DESCONHECIDO)\n", player.team);

        printf("  Position: (%.1f, %.1f, %.1f)\n", player.position.x, player.position.y, player.position.z);
        printf("  Distância: %.1f unidades\n", player.distance);

        // Indicador visual de proximidade
        if (player.distance < 100.0f)
            printf("  *** MUITO PRÓXIMO ***\n");
        else if (player.distance < 500.0f)
            printf("  ** PRÓXIMO **\n");
        else if (player.distance < 1000.0f)
            printf("  * MÉDIO *\n");
        else
            printf("  DISTANTE\n");

        printf("  Alive: %s\n", player.alive ? "Yes" : "No");
        printf("\n");
    }

    // Análise de ameaças
    int closeEnemies = 0;
    int closeAllies = 0;
    float closestDistance = 9999.0f;

    for (int i = 0; i < playerCount; i++)
    {
        const PlayerInfo& player = players[i];

        if (player.distance < closestDistance)
            closestDistance = player.distance;

        if (player.distance < 500.0f) // Próximo
        {
            if (localPlayer.valid && player.team != localPlayer.team && player.team != 0)
                closeEnemies++;
            else if (localPlayer.valid && player.team == localPlayer.team)
                closeAllies++;
        }
    }

    printf("[SimpleESP] === ANÁLISE TÁTICA ===\n");
    if (playerCount > 0)
    {
        printf("[SimpleESP] Jogador mais próximo: %.1f unidades\n", closestDistance);
        printf("[SimpleESP] Inimigos próximos: %d\n", closeEnemies);
        printf("[SimpleESP] Aliados próximos: %d\n", closeAllies);

        if (closeEnemies > 0)
            printf("[SimpleESP] ⚠️  ALERTA: %d INIMIGO(S) PRÓXIMO(S)!\n", closeEnemies);

        if (closestDistance < 100.0f)
            printf("[SimpleESP] 🚨 PERIGO: JOGADOR MUITO PRÓXIMO!\n");
    }

    printf("[SimpleESP] === RESULTADO FINAL ===\n");
    printf("[SimpleESP] Jogador Local: %s\n", localPlayer.valid ? "VÁLIDO" : "INVÁLIDO");
    printf("[SimpleESP] Outros Jogadores: %d\n", playerCount);
    printf("[SimpleESP] Total de Jogadores: %d\n", playerCount + (localPlayer.valid ? 1 : 0));
    printf("[SimpleESP] ========================\n");
}

bool SimpleESP::IsValidPointer(uintptr_t ptr)
{
    return (ptr != 0 && ptr > 0x10000 && ptr < 0x7FFFFFFFFFFF);
}
