#include "pch.h"
#include "simple_esp.h"

SimpleESP::SimpleESP() : m_pMemory(nullptr), m_hClient(nullptr), m_hE<PERSON>ine(nullptr), m_bRunning(false)
{
}

SimpleESP::~SimpleESP()
{
    Shutdown();
}

bool SimpleESP::Initialize()
{
    printf("[SimpleESP] Inicializando ESP simplificado...\n");
    
    // Inicializar sistema de memória
    m_pMemory = new Memory();
    if (!m_pMemory || !m_pMemory->Initialize())
    {
        printf("[SimpleESP] Falha ao inicializar sistema de memória!\n");
        return false;
    }
    
    // Encontrar módulos
    if (!FindModules())
    {
        printf("[SimpleESP] Falha ao encontrar módulos!\n");
        return false;
    }
    
    // Encontrar offsets
    if (!FindOffsets())
    {
        printf("[SimpleESP] Falha ao encontrar offsets!\n");
        return false;
    }
    
    m_bRunning = true;
    printf("[SimpleESP] ESP simplificado inicializado com sucesso!\n");
    return true;
}

void SimpleESP::Shutdown()
{
    m_bRunning = false;
    
    if (m_pMemory)
    {
        m_pMemory->Shutdown();
        delete m_pMemory;
        m_pMemory = nullptr;
    }
}

void SimpleESP::Run()
{
    printf("[SimpleESP] Iniciando loop principal...\n");
    printf("[SimpleESP] Pressione END para sair\n");
    
    int frameCounter = 0;
    
    while (m_bRunning && !GetAsyncKeyState(VK_END))
    {
        try
        {
            // Atualizar entidades a cada 60 frames (~1 segundo)
            if (frameCounter % 60 == 0)
            {
                UpdateEntities();
            }
            
            // Debug a cada 300 frames (~5 segundos)
            if (frameCounter % 300 == 0)
            {
                printf("[SimpleESP] ESP rodando... Frame: %d\n", frameCounter);
            }
            
            frameCounter++;
            Sleep(16); // ~60 FPS
        }
        catch (...)
        {
            printf("[SimpleESP] Erro no loop principal!\n");
            break;
        }
    }
    
    printf("[SimpleESP] Loop principal finalizado\n");
}

bool SimpleESP::FindModules()
{
    m_hClient = GetModuleHandleA("client.dll");
    m_hEngine = GetModuleHandleA("engine2.dll");
    
    printf("[SimpleESP] client.dll: 0x%p\n", m_hClient);
    printf("[SimpleESP] engine2.dll: 0x%p\n", m_hEngine);
    
    return (m_hClient != nullptr && m_hEngine != nullptr);
}

bool SimpleESP::FindOffsets()
{
    uintptr_t clientBase = (uintptr_t)m_hClient;
    
    // Offsets básicos (podem precisar ser atualizados)
    m_dwEntityList = clientBase + 0x1A044C0;
    m_dwLocalPlayerController = clientBase + 0x1A52D00;
    m_dwLocalPlayerPawn = clientBase + 0x18580D0;
    m_dwViewMatrix = clientBase + 0x1A6D260;
    
    printf("[SimpleESP] Offsets calculados:\n");
    printf("  EntityList: 0x%p\n", (void*)m_dwEntityList);
    printf("  LocalController: 0x%p\n", (void*)m_dwLocalPlayerController);
    printf("  LocalPawn: 0x%p\n", (void*)m_dwLocalPlayerPawn);
    printf("  ViewMatrix: 0x%p\n", (void*)m_dwViewMatrix);
    
    return true;
}

void SimpleESP::UpdateEntities()
{
    if (!m_pMemory)
        return;

    // Ler entity list
    uintptr_t entityList = m_pMemory->Read<uintptr_t>(m_dwEntityList);
    if (!IsValidPointer(entityList))
    {
        printf("[SimpleESP] EntityList inválido: 0x%p\n", (void*)entityList);
        return;
    }

    // Ler local player
    uintptr_t localController = m_pMemory->Read<uintptr_t>(m_dwLocalPlayerController);
    uintptr_t localPawn = m_pMemory->Read<uintptr_t>(m_dwLocalPlayerPawn);

    printf("[SimpleESP] LocalController: 0x%p, LocalPawn: 0x%p\n",
           (void*)localController, (void*)localPawn);

    // Contar entidades válidas e mostrar detalhes
    int validEntities = 0;
    int playersFound = 0;

    static bool showDetailedDebug = true;

    for (int i = 1; i <= 64; i++)
    {
        uintptr_t controller = m_pMemory->Read<uintptr_t>(entityList + (i * 0x78));
        if (IsValidPointer(controller))
        {
            validEntities++;

            // Ler informações detalhadas
            uintptr_t pawnHandle = m_pMemory->Read<uintptr_t>(controller + 0x824); // m_hPlayerPawn
            int health = m_pMemory->Read<int>(controller + 0x830);                 // m_iPawnHealth
            bool isAlive = m_pMemory->Read<bool>(controller + 0x82C);             // m_bPawnIsAlive
            int team = m_pMemory->Read<int>(controller + 0x3E3);                  // m_iTeamNum (aproximado)

            // Tentar ler nome do jogador
            char playerName[64] = {0};
            uintptr_t namePtr = controller + 0x770; // m_sSanitizedPlayerName (aproximado)
            for (int j = 0; j < 32; j++)
            {
                char c = m_pMemory->Read<char>(namePtr + j);
                if (c >= 32 && c <= 126) // Caracteres imprimíveis
                {
                    playerName[j] = c;
                }
                else
                {
                    break;
                }
            }

            // Verificar se é um jogador real (tem health > 0 ou nome válido)
            bool isRealPlayer = (health > 0 && health <= 100) || (strlen(playerName) > 0);

            if (isRealPlayer)
            {
                playersFound++;

                // Mostrar detalhes dos primeiros 10 jogadores reais
                if (playersFound <= 10 && showDetailedDebug)
                {
                    printf("[SimpleESP] Player[%d]: Controller=0x%p\n", playersFound, (void*)controller);
                    printf("  Handle=0x%X, Health=%d, Alive=%s, Team=%d\n",
                           pawnHandle, health, isAlive ? "Yes" : "No", team);
                    printf("  Name='%s' (len=%d)\n", playerName, (int)strlen(playerName));

                    // Tentar resolver o pawn handle
                    if (pawnHandle != 0)
                    {
                        uintptr_t pawnIndex = pawnHandle & 0x7FFFFFFF;
                        uintptr_t pawn = m_pMemory->Read<uintptr_t>(entityList + 0x10 + (pawnIndex * 0x78));
                        printf("  PawnIndex=%d, Pawn=0x%p\n", (int)pawnIndex, (void*)pawn);

                        if (IsValidPointer(pawn))
                        {
                            // Ler health diretamente do pawn
                            int pawnHealth = m_pMemory->Read<int>(pawn + 0x344); // m_iHealth
                            int pawnTeam = m_pMemory->Read<int>(pawn + 0x3E3);   // m_iTeamNum
                            printf("  PawnHealth=%d, PawnTeam=%d\n", pawnHealth, pawnTeam);
                        }
                    }
                    printf("\n");
                }
            }
        }
    }

    printf("[SimpleESP] Entidades válidas: %d, Jogadores reais: %d\n", validEntities, playersFound);

    // Desabilitar debug detalhado após primeira execução para não spammar
    showDetailedDebug = false;
}

void SimpleESP::PrintEntityInfo()
{
    // Implementar se necessário
}

bool SimpleESP::IsValidPointer(uintptr_t ptr)
{
    if (ptr == 0)
        return false;
    
    // Verificação básica de range de memória
    if (ptr < 0x10000 || ptr > 0x7FFFFFFFFFFF)
        return false;
    
    return m_pMemory && m_pMemory->IsValidAddress(ptr);
}
