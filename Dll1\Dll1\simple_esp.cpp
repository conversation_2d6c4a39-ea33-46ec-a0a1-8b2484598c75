#include "pch.h"
#include "simple_esp.h"

SimpleESP::SimpleESP() : m_pM<PERSON>ory(nullptr), m_pScan<PERSON>(nullptr), m_hClient(nullptr), m_hE<PERSON>ine(nullptr), m_bRunning(false)
{
}

SimpleESP::~SimpleESP()
{
    Shutdown();
}

bool SimpleESP::Initialize()
{
    printf("[SimpleESP] Inicializando ESP simplificado...\n");
    
    // Inicializar sistema de memória
    m_pMemory = new Memory();
    if (!m_pMemory || !m_pMemory->Initialize())
    {
        printf("[SimpleESP] Falha ao inicializar sistema de memória!\n");
        return false;
    }
    
    // Encontrar módulos
    if (!FindModules())
    {
        printf("[SimpleESP] Falha ao encontrar módulos!\n");
        return false;
    }
    
    // Tentar encontrar offsets com pattern scanning primeiro
    if (!FindOffsetsWithPatterns())
    {
        printf("[SimpleESP] Pattern scanning falhou, usando offsets fixos...\n");
        if (!FindOffsets())
        {
            printf("[SimpleESP] Falha ao encontrar offsets!\n");
            return false;
        }
    }
    
    m_bRunning = true;
    printf("[SimpleESP] ESP simplificado inicializado com sucesso!\n");
    return true;
}

void SimpleESP::Shutdown()
{
    m_bRunning = false;

    if (m_pScanner)
    {
        delete m_pScanner;
        m_pScanner = nullptr;
    }

    if (m_pMemory)
    {
        m_pMemory->Shutdown();
        delete m_pMemory;
        m_pMemory = nullptr;
    }
}

void SimpleESP::Run()
{
    printf("[SimpleESP] Iniciando loop principal...\n");
    printf("[SimpleESP] Pressione END para sair\n");
    
    int frameCounter = 0;
    
    while (m_bRunning && !GetAsyncKeyState(VK_END))
    {
        try
        {
            // Atualizar entidades a cada 60 frames (~1 segundo)
            if (frameCounter % 60 == 0)
            {
                UpdateEntities();
            }
            
            // Debug a cada 300 frames (~5 segundos)
            if (frameCounter % 300 == 0)
            {
                printf("[SimpleESP] ESP rodando... Frame: %d\n", frameCounter);
            }
            
            frameCounter++;
            Sleep(16); // ~60 FPS
        }
        catch (...)
        {
            printf("[SimpleESP] Erro no loop principal!\n");
            break;
        }
    }
    
    printf("[SimpleESP] Loop principal finalizado\n");
}

bool SimpleESP::FindModules()
{
    m_hClient = GetModuleHandleA("client.dll");
    m_hEngine = GetModuleHandleA("engine2.dll");
    
    printf("[SimpleESP] client.dll: 0x%p\n", m_hClient);
    printf("[SimpleESP] engine2.dll: 0x%p\n", m_hEngine);
    
    return (m_hClient != nullptr && m_hEngine != nullptr);
}

bool SimpleESP::FindOffsetsWithPatterns()
{
    printf("[SimpleESP] === PATTERN SCANNING ===\n");

    // Inicializar pattern scanner
    m_pScanner = new PatternScanner((uintptr_t)m_hClient);
    if (!m_pScanner->Initialize())
    {
        delete m_pScanner;
        m_pScanner = nullptr;
        return false;
    }

    // Encontrar offsets dinamicamente
    uintptr_t entityList = m_pScanner->FindEntityList();
    uintptr_t localController = m_pScanner->FindLocalPlayerController();
    uintptr_t localPawn = m_pScanner->FindLocalPlayerPawn();
    uintptr_t viewMatrix = m_pScanner->FindViewMatrix();

    // Verificar se encontrou pelo menos EntityList
    if (entityList == 0)
    {
        printf("[SimpleESP] Pattern scanning falhou - EntityList não encontrado!\n");
        return false;
    }

    // Usar os offsets encontrados
    m_dwEntityList = entityList;
    m_dwLocalPlayerController = localController ? localController : ((uintptr_t)m_hClient + 0x1A52D00);
    m_dwLocalPlayerPawn = localPawn ? localPawn : ((uintptr_t)m_hClient + 0x18580D0);
    m_dwViewMatrix = viewMatrix ? viewMatrix : ((uintptr_t)m_hClient + 0x1A6D260);

    printf("[SimpleESP] === OFFSETS ENCONTRADOS ===\n");
    printf("  EntityList: 0x%p %s\n", (void*)m_dwEntityList, entityList ? "(PATTERN)" : "(FIXO)");
    printf("  LocalController: 0x%p %s\n", (void*)m_dwLocalPlayerController, localController ? "(PATTERN)" : "(FIXO)");
    printf("  LocalPawn: 0x%p %s\n", (void*)m_dwLocalPlayerPawn, localPawn ? "(PATTERN)" : "(FIXO)");
    printf("  ViewMatrix: 0x%p %s\n", (void*)m_dwViewMatrix, viewMatrix ? "(PATTERN)" : "(FIXO)");
    printf("[SimpleESP] ========================\n");

    return true;
}

bool SimpleESP::FindOffsets()
{
    uintptr_t clientBase = (uintptr_t)m_hClient;

    // Offsets básicos (fallback)
    m_dwEntityList = clientBase + 0x1A044C0;
    m_dwLocalPlayerController = clientBase + 0x1A52D00;
    m_dwLocalPlayerPawn = clientBase + 0x18580D0;
    m_dwViewMatrix = clientBase + 0x1A6D260;

    printf("[SimpleESP] Usando offsets fixos (fallback):\n");
    printf("  EntityList: 0x%p\n", (void*)m_dwEntityList);
    printf("  LocalController: 0x%p\n", (void*)m_dwLocalPlayerController);
    printf("  LocalPawn: 0x%p\n", (void*)m_dwLocalPlayerPawn);
    printf("  ViewMatrix: 0x%p\n", (void*)m_dwViewMatrix);

    return true;
}

void SimpleESP::UpdateEntities()
{
    if (!m_pMemory)
        return;

    // Verificar se está em jogo
    uintptr_t localController = m_pMemory->Read<uintptr_t>(m_dwLocalPlayerController);
    uintptr_t localPawn = m_pMemory->Read<uintptr_t>(m_dwLocalPlayerPawn);

    bool inGame = (localController != 0 && localPawn != 0);

    printf("[SimpleESP] Status: %s | LocalController: 0x%p, LocalPawn: 0x%p\n",
           inGame ? "EM JOGO" : "FORA DO JOGO", (void*)localController, (void*)localPawn);

    if (!inGame)
    {
        printf("[SimpleESP] Nao esta em uma partida! Entre em um servidor/mapa.\n");
        return;
    }

    // NOVO: Analisar LocalPawn para encontrar offsets corretos
    static bool offsetsFound = false;
    static uintptr_t correctHealthOffset = 0x344;
    static uintptr_t correctTeamOffset = 0x3E3;

    if (!offsetsFound && localPawn != 0)
    {
        printf("[SimpleESP] === ANALISE DO LOCAL PAWN ===\n");
        printf("[SimpleESP] LocalPawn: 0x%p\n", (void*)localPawn);

        // Testar offsets de health no LocalPawn
        uintptr_t healthOffsets[] = { 0x344, 0x340, 0x348, 0x33C, 0x350, 0x354, 0x338, 0x35C };
        for (int h = 0; h < 8; h++)
        {
            int health = m_pMemory->Read<int>(localPawn + healthOffsets[h]);
            if (health > 0 && health <= 100)
            {
                correctHealthOffset = healthOffsets[h];
                printf("[SimpleESP] LocalPawn Health correto: offset 0x%X = %d\n", healthOffsets[h], health);
                break;
            }
        }

        // Testar offsets de team no LocalPawn
        uintptr_t teamOffsets[] = { 0x3E3, 0x3E0, 0x3E4, 0x3E8, 0x3DC, 0x3EC, 0x3D8, 0x3F0 };
        for (int t = 0; t < 8; t++)
        {
            int team = m_pMemory->Read<int>(localPawn + teamOffsets[t]);
            if (team == 2 || team == 3)
            {
                correctTeamOffset = teamOffsets[t];
                printf("[SimpleESP] LocalPawn Team correto: offset 0x%X = %d\n", teamOffsets[t], team);
                break;
            }
        }

        offsetsFound = true;
        printf("[SimpleESP] === OFFSETS ENCONTRADOS ===\n");
        printf("[SimpleESP] Health: 0x%X, Team: 0x%X\n", correctHealthOffset, correctTeamOffset);
        printf("[SimpleESP] ==============================\n");
    }

    // Ler entity list
    uintptr_t entityList = m_pMemory->Read<uintptr_t>(m_dwEntityList);
    if (!IsValidPointer(entityList))
    {
        printf("[SimpleESP] EntityList inválido: 0x%p\n", (void*)entityList);
        return;
    }

    // Contar entidades válidas e jogadores reais
    int validControllers = 0;
    int realPlayers = 0;

    printf("[SimpleESP] === ANALISE DE ENTIDADES ===\n");

    for (int i = 1; i <= 64; i++)
    {
        uintptr_t controller = m_pMemory->Read<uintptr_t>(entityList + (i * 0x78));
        if (!IsValidPointer(controller))
            continue;

        validControllers++;

        // Testar diferentes offsets conhecidos do CS2 (2024/2025)
        uintptr_t pawnHandle = 0;
        int controllerHealth = 0;
        bool isAlive = false;

        // Offsets conhecidos para m_hPlayerPawn
        uintptr_t pawnOffsets[] = { 0x824, 0x7FC, 0x800, 0x804, 0x808, 0x80C, 0x810, 0x814, 0x818, 0x81C, 0x820, 0x828, 0x82C, 0x830 };

        // Testar cada offset até encontrar um handle válido
        for (int p = 0; p < 14; p++)
        {
            uintptr_t testHandle = m_pMemory->Read<uintptr_t>(controller + pawnOffsets[p]);

            // Verificar se é um handle válido do CS2
            if (testHandle != 0 &&
                testHandle != 0x1E8 &&
                testHandle != 0x2BD &&
                testHandle != 0x130 &&
                ((testHandle & 0x80000000) || ((testHandle & 0x7FFFFFFF) > 0 && (testHandle & 0x7FFFFFFF) < 2048)))
            {
                pawnHandle = testHandle;

                if (validControllers <= 3)
                {
                    printf("  [DEBUG] Handle válido encontrado no offset 0x%X: 0x%X\n", pawnOffsets[p], testHandle);
                }
                break;
            }
        }

        // Testar offsets para health do controller
        uintptr_t healthOffsets[] = { 0x830, 0x834, 0x838, 0x82C, 0x828, 0x824, 0x820, 0x83C, 0x840 };
        for (int h = 0; h < 9; h++)
        {
            int testHealth = m_pMemory->Read<int>(controller + healthOffsets[h]);
            if (testHealth > 0 && testHealth <= 100)
            {
                controllerHealth = testHealth;
                if (validControllers <= 3)
                {
                    printf("  [DEBUG] Health válido encontrado no offset 0x%X: %d\n", healthOffsets[h], testHealth);
                }
                break;
            }
        }

        // Testar offsets para alive
        uintptr_t aliveOffsets[] = { 0x82C, 0x828, 0x824, 0x830, 0x834, 0x820, 0x81C };
        for (int a = 0; a < 7; a++)
        {
            bool testAlive = m_pMemory->Read<bool>(controller + aliveOffsets[a]);
            if (testAlive) // Se encontrou alive = true
            {
                isAlive = testAlive;
                if (validControllers <= 3)
                {
                    printf("  [DEBUG] Alive encontrado no offset 0x%X: %s\n", aliveOffsets[a], testAlive ? "true" : "false");
                }
                break;
            }
        }

        // Tentar diferentes métodos para resolver o pawn
        uintptr_t pawn = 0;

        // Debug detalhado para primeiras entidades
        if (validControllers <= 3)
        {
            printf("  [DEBUG] Controller[%d]: Handle=0x%X, Health=%d, Alive=%s\n",
                   validControllers, pawnHandle, controllerHealth, isAlive ? "Yes" : "No");
        }

        // NOVO: Método direto - usar EntityList para encontrar pawns
        if (pawnHandle != 0)
        {
            // Método 1: Buscar diretamente na EntityList
            for (int entityIndex = 1; entityIndex <= 64; entityIndex++)
            {
                uintptr_t testEntity = m_pMemory->Read<uintptr_t>(entityList + (entityIndex * 0x78));
                if (IsValidPointer(testEntity))
                {
                    // Verificar se este entity tem dados válidos de pawn
                    int testHealth = m_pMemory->Read<int>(testEntity + correctHealthOffset);
                    int testTeam = m_pMemory->Read<int>(testEntity + correctTeamOffset);

                    if ((testHealth > 0 && testHealth <= 100) && (testTeam == 2 || testTeam == 3))
                    {
                        pawn = testEntity;
                        if (validControllers <= 3)
                        {
                            printf("  [DEBUG] Método 1: EntityIndex=%d, Pawn=0x%p (H=%d, T=%d)\n",
                                   entityIndex, (void*)pawn, testHealth, testTeam);
                        }
                        break;
                    }
                }
            }
        }

        // Método 2: Se não encontrou, tentar handle tradicional
        if (!IsValidPointer(pawn) && pawnHandle != 0)
        {
            uintptr_t pawnIndex = pawnHandle & 0x7FFFFFFF;
            if (pawnIndex > 0 && pawnIndex < 2048)
            {
                pawn = m_pMemory->Read<uintptr_t>(entityList + 0x10 + (pawnIndex * 0x78));
                if (validControllers <= 3)
                {
                    printf("  [DEBUG] Método 2: Index=%d, Pawn=0x%p\n", (int)pawnIndex, (void*)pawn);
                }
            }
        }

        // Método 3: Tentar usar LocalController como referência
        if (!IsValidPointer(pawn) && pawnHandle != 0 && localController != 0)
        {
            // Se este é o LocalController, usar LocalPawn diretamente
            if (controller == localController)
            {
                pawn = localPawn;
                if (validControllers <= 3)
                {
                    printf("  [DEBUG] Método 3: LocalController detectado, usando LocalPawn=0x%p\n", (void*)pawn);
                }
            }
            else
            {
                // Para outros controllers, tentar calcular offset baseado no LocalController
                uintptr_t localHandle = 0;
                for (int p = 0; p < 14; p++)
                {
                    uintptr_t testHandle = m_pMemory->Read<uintptr_t>(localController + pawnOffsets[p]);
                    if (testHandle != 0 && testHandle != 0x1E8 && testHandle != 0x2BD && testHandle != 0x130)
                    {
                        localHandle = testHandle;
                        break;
                    }
                }

                if (localHandle != 0)
                {
                    // Calcular diferença entre handles e aplicar ao LocalPawn
                    int handleDiff = (int)pawnHandle - (int)localHandle;
                    pawn = localPawn + (handleDiff * 0x1000); // Estimativa

                    if (validControllers <= 3)
                    {
                        printf("  [DEBUG] Método 3: Calculado baseado em LocalController, Pawn=0x%p\n", (void*)pawn);
                    }
                }
            }
        }

        // Método 4: Handle direto (último recurso)
        if (!IsValidPointer(pawn) && pawnHandle != 0)
        {
            pawn = pawnHandle;
            if (validControllers <= 3)
            {
                printf("  [DEBUG] Método 4: Handle direto, Pawn=0x%p\n", (void*)pawn);
            }
        }

        // Verificar se é um jogador real
        bool isRealPlayer = false;
        int pawnHealth = 0;
        int pawnTeam = 0;

        if (IsValidPointer(pawn))
        {
            // Usar offsets corretos encontrados do LocalPawn
            pawnHealth = m_pMemory->Read<int>(pawn + correctHealthOffset);
            pawnTeam = m_pMemory->Read<int>(pawn + correctTeamOffset);

            // Debug detalhado para primeira entidade
            if (validControllers <= 3)
            {
                printf("  [DEBUG] Pawn 0x%p: Health=%d, Team=%d (offsets: 0x%X, 0x%X)\n",
                       (void*)pawn, pawnHealth, pawnTeam, correctHealthOffset, correctTeamOffset);
            }

            // Validar se é jogador real
            isRealPlayer = (pawnHealth > 0 && pawnHealth <= 100) &&  // Health válido
                          (pawnTeam == 2 || pawnTeam == 3);          // Team válido (T ou CT)
        }

        // FALLBACK: Se não conseguiu resolver pawn, mas tem health do controller válido
        if (!isRealPlayer && controllerHealth > 0 && controllerHealth <= 100)
        {
            isRealPlayer = true;
            pawnHealth = controllerHealth; // Usar health do controller
            pawnTeam = 0; // Team desconhecido, mas jogador válido

            if (validControllers <= 3)
            {
                printf("  [DEBUG] FALLBACK: Usando dados do controller (Health=%d)\n", controllerHealth);
            }
        }

        if (isRealPlayer)
        {
            realPlayers++;
            printf("[SimpleESP] JOGADOR[%d]:\n", realPlayers);
            printf("  Controller: 0x%p\n", (void*)controller);
            printf("  PawnHandle: 0x%X -> Pawn: 0x%p\n", pawnHandle, (void*)pawn);
            printf("  Health: %d (Controller: %d)\n", pawnHealth, controllerHealth);
            printf("  Team: %d, Alive: %s\n", pawnTeam, isAlive ? "Yes" : "No");
            printf("\n");
        }
        else if (validControllers <= 10) // Debug das primeiras 10 entidades
        {
            printf("[SimpleESP] Entity[%d]: Controller=0x%p, Handle=0x%X\n",
                   i, (void*)controller, pawnHandle);
            printf("  Health: %d/%d, Team: %d, Alive: %s, Pawn: 0x%p\n",
                   pawnHealth, controllerHealth, pawnTeam, isAlive ? "Yes" : "No", (void*)pawn);
        }
    }

    printf("[SimpleESP] === RESULTADO ===\n");
    printf("[SimpleESP] Controllers válidos: %d\n", validControllers);
    printf("[SimpleESP] Jogadores reais: %d\n", realPlayers);
    printf("[SimpleESP] ==================\n");
}

void SimpleESP::PrintEntityInfo()
{
    // Implementar se necessário
}

bool SimpleESP::IsValidPointer(uintptr_t ptr)
{
    if (ptr == 0)
        return false;
    
    // Verificação básica de range de memória
    if (ptr < 0x10000 || ptr > 0x7FFFFFFFFFFF)
        return false;
    
    return m_pMemory && m_pMemory->IsValidAddress(ptr);
}
