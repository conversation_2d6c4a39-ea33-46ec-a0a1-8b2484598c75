name: build

on:
  push:
  pull_request:
  workflow_run:
    # Use a workflow as a trigger of scheduled builds. Forked repositories can disable scheduled builds by disabling
    # "scheduled" workflow, while maintaining ability to perform local CI builds.
    workflows:
      - scheduled
      - manual
    branches:
      - master
      - docking
    types:
      - requested

jobs:
  Windows:
    runs-on: windows-2019
    env:
      VS_PATH: C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\
      MSBUILD_PATH: C:\Program Files (x86)\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\
    steps:
      - uses: actions/checkout@v4

      - name: Install Dependencies
        shell: powershell
        run: |
          Invoke-WebRequest -Uri "https://www.libsdl.org/release/SDL2-devel-2.26.3-VC.zip" -OutFile "SDL2-devel-2.26.3-VC.zip"
          Expand-Archive -Path SDL2-devel-2.26.3-VC.zip
          echo "SDL2_DIR=$(pwd)\SDL2-devel-2.26.3-VC\SDL2-2.26.3\" >>${env:GITHUB_ENV}

          Invoke-WebRequest -Uri "https://github.com/ocornut/imgui/files/3789205/vulkan-sdk-*********.zip" -OutFile vulkan-sdk-*********.zip
          Expand-Archive -Path vulkan-sdk-*********.zip
          echo "VULKAN_SDK=$(pwd)\vulkan-sdk-*********\" >>${env:GITHUB_ENV}

      - name: Fix Projects
        shell: powershell
        run: |
          # CI workers do not supporter older Visual Studio versions. Fix projects to target newer available version.
          gci -recurse -filter "*.vcxproj" | ForEach-Object {
            (Get-Content $_.FullName) -Replace "<PlatformToolset>v\d{3}</PlatformToolset>","<PlatformToolset>v142</PlatformToolset>" | Set-Content -Path $_.FullName
            (Get-Content $_.FullName) -Replace "<WindowsTargetPlatformVersion>[\d\.]+</WindowsTargetPlatformVersion>","<WindowsTargetPlatformVersion>10.0.18362.0</WindowsTargetPlatformVersion>" | Set-Content -Path $_.FullName
          }

      # Not using matrix here because it would inflate job count too much. Check out and setup is done for every job and that makes build times way too long.
      - name: Build example_null (extra warnings, mingw 64-bit)
        run: mingw32-make -C examples/example_null WITH_EXTRA_WARNINGS=1

      - name: Build example_null (mingw 64-bit, as DLL)
        shell: bash
        run: |
          echo '#define IMGUI_API __declspec(dllexport)'         >  example_single_file.cpp
          echo '#define IMGUI_IMPLEMENTATION'                    >> example_single_file.cpp
          echo '#include "misc/single_file/imgui_single_file.h"' >> example_single_file.cpp
          g++ -I. -Wall -Wformat -shared -o libimgui.dll -Wl,--out-implib,libimgui.a example_single_file.cpp -limm32
          g++ -I. -Wall -Wformat -DIMGUI_API='__declspec(dllimport)' -o example_null.exe examples/example_null/main.cpp -L. -limgui
          rm -f example_null.exe libimgui.* example_single_file.*

      - name: Build example_null (extra warnings, msvc 64-bit)
        shell: cmd
        run: |
          cd examples\example_null
          call "%VS_PATH%\VC\Auxiliary\Build\vcvars64.bat"
          .\build_win32.bat /W4

      - name: Build example_null (single file build)
        shell: bash
        run: |
          cat > example_single_file.cpp <<'EOF'

          #define IMGUI_IMPLEMENTATION
          #include "misc/single_file/imgui_single_file.h"
          #include "examples/example_null/main.cpp"

          EOF
          g++ -I. -Wall -Wformat -o example_single_file.exe example_single_file.cpp -limm32

      - name: Build example_null (with IMGUI_DISABLE_WIN32_FUNCTIONS)
        shell: bash
        run: |
          cat > example_single_file.cpp <<'EOF'

          #define IMGUI_DISABLE_WIN32_FUNCTIONS
          #define IMGUI_IMPLEMENTATION
          #include "misc/single_file/imgui_single_file.h"
          #include "examples/example_null/main.cpp"

          EOF
          g++ -I. -Wall -Wformat -o example_single_file.exe example_single_file.cpp -limm32

      - name: Build example_null (as DLL)
        shell: cmd
        run: |
          call "%VS_PATH%\VC\Auxiliary\Build\vcvars64.bat"

          echo #define IMGUI_API __declspec(dllexport)         >  example_single_file.cpp
          echo #define IMGUI_IMPLEMENTATION                    >> example_single_file.cpp
          echo #include "misc/single_file/imgui_single_file.h" >> example_single_file.cpp

          cl.exe /D_USRDLL /D_WINDLL /I. example_single_file.cpp /LD /FeImGui.dll /link
          cl.exe /DIMGUI_API=__declspec(dllimport) /I. ImGui.lib /Feexample_null.exe examples/example_null/main.cpp

      - name: Build Win32 example_glfw_opengl2
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_glfw_opengl2/example_glfw_opengl2.vcxproj /p:Platform=Win32 /p:Configuration=Release'

      - name: Build Win32 example_glfw_opengl3
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_glfw_opengl3/example_glfw_opengl3.vcxproj /p:Platform=Win32 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build Win32 example_glfw_vulkan
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_glfw_vulkan/example_glfw_vulkan.vcxproj /p:Platform=Win32 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build Win32 example_sdl2_sdlrenderer2
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_sdl2_sdlrenderer2/example_sdl2_sdlrenderer2.vcxproj /p:Platform=Win32 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build Win32 example_sdl2_vulkan
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_sdl2_vulkan/example_sdl2_vulkan.vcxproj /p:Platform=Win32 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build Win32 example_sdl2_opengl2
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_sdl2_opengl2/example_sdl2_opengl2.vcxproj /p:Platform=Win32 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build Win32 example_sdl2_opengl3
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_sdl2_opengl3/example_sdl2_opengl3.vcxproj /p:Platform=Win32 /p:Configuration=Release'

      - name: Build Win32 example_sdl2_directx11
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_sdl2_directx11/example_sdl2_directx11.vcxproj /p:Platform=Win32 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build Win32 example_win32_directx9
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_win32_directx9/example_win32_directx9.vcxproj /p:Platform=Win32 /p:Configuration=Release'

      - name: Build Win32 example_win32_directx10
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_win32_directx10/example_win32_directx10.vcxproj /p:Platform=Win32 /p:Configuration=Release'

      - name: Build Win32 example_win32_directx11
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_win32_directx11/example_win32_directx11.vcxproj /p:Platform=Win32 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build x64 example_glfw_opengl2
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_glfw_opengl2/example_glfw_opengl2.vcxproj /p:Platform=x64 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build x64 example_glfw_opengl3
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_glfw_opengl3/example_glfw_opengl3.vcxproj /p:Platform=x64 /p:Configuration=Release'

      - name: Build x64 example_glfw_vulkan
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_glfw_vulkan/example_glfw_vulkan.vcxproj /p:Platform=x64 /p:Configuration=Release'

      - name: Build x64 example_sdl2_sdlrenderer2
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_sdl2_sdlrenderer2/example_sdl2_sdlrenderer2.vcxproj /p:Platform=x64 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build x64 example_sdl2_vulkan
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_sdl2_vulkan/example_sdl2_vulkan.vcxproj /p:Platform=x64 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build x64 example_sdl2_opengl2
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_sdl2_opengl2/example_sdl2_opengl2.vcxproj /p:Platform=x64 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build x64 example_sdl2_opengl3
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_sdl2_opengl3/example_sdl2_opengl3.vcxproj /p:Platform=x64 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build x64 example_sdl2_directx11
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_sdl2_directx11/example_sdl2_directx11.vcxproj /p:Platform=x64 /p:Configuration=Release'

      - name: Build x64 example_win32_directx9
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_win32_directx9/example_win32_directx9.vcxproj /p:Platform=x64 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build x64 example_win32_directx10
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_win32_directx10/example_win32_directx10.vcxproj /p:Platform=x64 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build x64 example_win32_directx11
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_win32_directx11/example_win32_directx11.vcxproj /p:Platform=x64 /p:Configuration=Release'
        if: github.event_name == 'workflow_run'

      - name: Build x64 example_win32_directx12
        shell: cmd
        run: '"%MSBUILD_PATH%\MSBuild.exe" examples/example_win32_directx12/example_win32_directx12.vcxproj /p:Platform=x64 /p:Configuration=Release'

  Linux:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Install Dependencies
      run: |
        sudo apt-get update
        sudo apt-get install -y libglfw3-dev libsdl2-dev gcc-multilib g++-multilib libfreetype6-dev libvulkan-dev

    - name: Build example_null (extra warnings, gcc 32-bit)
      run: |
        make -C examples/example_null clean
        CXXFLAGS="$CXXFLAGS -m32 -Werror" make -C examples/example_null WITH_EXTRA_WARNINGS=1

    - name: Build example_null (extra warnings, gcc 64-bit)
      run: |
        make -C examples/example_null clean
        CXXFLAGS="$CXXFLAGS -m64 -Werror" make -C examples/example_null WITH_EXTRA_WARNINGS=1

    - name: Build example_null (extra warnings, clang 32-bit)
      run: |
        make -C examples/example_null clean
        CXXFLAGS="$CXXFLAGS -m32 -Werror" CXX=clang++ make -C examples/example_null WITH_EXTRA_WARNINGS=1

    - name: Build example_null (extra warnings, clang 64-bit)
      run: |
        make -C examples/example_null clean
        CXXFLAGS="$CXXFLAGS -m64 -Werror" CXX=clang++ make -C examples/example_null WITH_EXTRA_WARNINGS=1

    - name: Build example_null (extra warnings, empty IM_ASSERT)
      run: |
          cat > example_single_file.cpp <<'EOF'

          #define IM_ASSERT(x)
          #define IMGUI_IMPLEMENTATION
          #include "misc/single_file/imgui_single_file.h"
          #include "examples/example_null/main.cpp"

          EOF
          g++ -I. -std=c++11 -Wall -Wformat -Wextra -Werror -Wno-zero-as-null-pointer-constant -Wno-double-promotion -Wno-variadic-macros -Wno-empty-body -o example_single_file example_single_file.cpp

    - name: Build example_null (freetype)
      run: |
        make -C examples/example_null clean
        make -C examples/example_null WITH_FREETYPE=1

    - name: Build example_null (single file build)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define IMGUI_IMPLEMENTATION
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        g++ -I. -std=c++11 -Wall -Wformat -o example_single_file example_single_file.cpp

    - name: Build example_null (with ImWchar32)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define IMGUI_USE_WCHAR32
        #define IMGUI_IMPLEMENTATION
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        g++ -I. -std=c++11 -Wall -Wformat -o example_single_file example_single_file.cpp

    - name: Build example_null (with large ImDrawIdx + pointer ImTextureID)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define ImTextureID void*
        #define ImDrawIdx unsigned int
        #define IMGUI_IMPLEMENTATION
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        g++ -I. -std=c++11 -Wall -Wformat -o example_single_file example_single_file.cpp

    - name: Build example_null (with IMGUI_DISABLE_OBSOLETE_FUNCTIONS)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define IMGUI_DISABLE_OBSOLETE_FUNCTIONS
        #define IMGUI_IMPLEMENTATION
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        g++ -I. -std=c++11 -Wall -Wformat -o example_single_file example_single_file.cpp

    - name: Build example_null (with IMGUI_DISABLE_OBSOLETE_KEYIO)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define IMGUI_DISABLE_OBSOLETE_KEYIO
        #define IMGUI_IMPLEMENTATION
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        g++ -I. -std=c++11 -Wall -Wformat -o example_single_file example_single_file.cpp

    - name: Build example_null (with C++20)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define IMGUI_DISABLE_OBSOLETE_KEYIO
        #define IMGUI_IMPLEMENTATION
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        g++ -I. -std=c++20 -Wall -Wformat -o example_single_file example_single_file.cpp

    - name: Build example_null (with IMGUI_DISABLE_DEMO_WINDOWS and IMGUI_DISABLE_DEBUG_TOOLS)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define IMGUI_DISABLE_DEMO_WINDOWS
        #define IMGUI_DISABLE_DEBUG_TOOLS
        #define IMGUI_IMPLEMENTATION
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        g++ -I. -std=c++11 -Wall -Wformat -o example_single_file example_single_file.cpp

    - name: Build example_null (with IMGUI_DISABLE_FILE_FUNCTIONS)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define IMGUI_DISABLE_FILE_FUNCTIONS
        #define IMGUI_IMPLEMENTATION
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        g++ -I. -std=c++11 -Wall -Wformat -o example_single_file example_single_file.cpp

    - name: Build example_null (with IMGUI_USE_BGRA_PACKED_COLOR)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define IMGUI_USE_BGRA_PACKED_COLOR
        #define IMGUI_IMPLEMENTATION
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        g++ -I. -std=c++11 -Wall -Wformat -o example_single_file example_single_file.cpp

    - name: Build example_null (with IM_VEC2_CLASS_EXTRA and IM_VEC4_CLASS_EXTRA)
      run: |
        cat > example_single_file.cpp <<'EOF'

        struct MyVec2 { float x; float y; MyVec2(float x, float y) : x(x), y(y) { } };
        struct MyVec4 { float x; float y; float z; float w;
        MyVec4(float x, float y, float z, float w) : x(x), y(y), z(z), w(w) { } };
        #define IM_VEC2_CLASS_EXTRA                                             \
                ImVec2(const MyVec2& f) { x = f.x; y = f.y; }                   \
                operator MyVec2() const { return MyVec2(x, y); }
        #define IM_VEC4_CLASS_EXTRA                                             \
                ImVec4(const MyVec4& f) { x = f.x; y = f.y; z = f.z; w = f.w; } \
                operator MyVec4() const { return MyVec4(x, y, z, w); }
        #define IMGUI_IMPLEMENTATION
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        g++ -I. -std=c++11 -Wall -Wformat -o example_single_file example_single_file.cpp

    - name: Build example_null (C++26, Clang)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define IMGUI_IMPLEMENTATION
        #define IMGUI_DISABLE_DEMO_WINDOWS
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        clang++ -I. -std=c++26 -Wall -Wformat -fno-exceptions -fno-threadsafe-statics -lc -lm -o example_single_file example_single_file.cpp

    - name: Build example_null (without c++ runtime, Clang)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define IMGUI_IMPLEMENTATION
        #define IMGUI_DISABLE_DEMO_WINDOWS
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        clang++ -I. -std=c++11 -Wall -Wformat -nodefaultlibs -fno-rtti -fno-exceptions -fno-threadsafe-statics -lc -lm -o example_single_file example_single_file.cpp

    - name: Build example_glfw_opengl2
      run: make -C examples/example_glfw_opengl2

    - name: Build example_glfw_opengl3
      run: make -C examples/example_glfw_opengl3
      if: github.event_name == 'workflow_run'

    - name: Build example_sdl2_opengl2
      run: make -C examples/example_sdl2_opengl2
      if: github.event_name == 'workflow_run'

    - name: Build example_sdl2_opengl3
      run: make -C examples/example_sdl2_opengl3

    - name: Build with IMGUI_IMPL_VULKAN_NO_PROTOTYPES
      run: g++ -c -I. -std=c++11 -DIMGUI_IMPL_VULKAN_NO_PROTOTYPES=1 backends/imgui_impl_vulkan.cpp

  MacOS:
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v4

    - name: Install Dependencies
      run: |
        brew install glfw3 sdl2

    - name: Build example_null (extra warnings, clang 64-bit)
      run: make -C examples/example_null WITH_EXTRA_WARNINGS=1

    - name: Build example_null (single file build)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define IMGUI_IMPLEMENTATION
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        clang++ -I. -std=c++11 -Wall -Wformat -o example_single_file example_single_file.cpp

    - name: Build example_null (single file build, c++20)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define IMGUI_IMPLEMENTATION
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        clang++ -I. -std=c++20 -Wall -Wformat -o example_single_file example_single_file.cpp

    - name: Build example_null (without c++ runtime)
      run: |
        cat > example_single_file.cpp <<'EOF'

        #define IMGUI_IMPLEMENTATION
        #include "misc/single_file/imgui_single_file.h"
        #include "examples/example_null/main.cpp"

        EOF
        clang++ -I. -std=c++11 -Wall -Wformat -nodefaultlibs -fno-rtti -fno-exceptions -fno-threadsafe-statics -lc -lm -o example_single_file example_single_file.cpp

    - name: Build example_glfw_opengl2
      run: make -C examples/example_glfw_opengl2

    - name: Build example_glfw_opengl3
      run: make -C examples/example_glfw_opengl3
      if: github.event_name == 'workflow_run'

    - name: Build example_glfw_metal
      run: make -C examples/example_glfw_metal

    - name: Build example_sdl2_metal
      run: make -C examples/example_sdl2_metal

    - name: Build example_sdl2_opengl2
      run: make -C examples/example_sdl2_opengl2
      if: github.event_name == 'workflow_run'

    - name: Build example_sdl2_opengl3
      run: make -C examples/example_sdl2_opengl3

    - name: Build example_apple_metal
      run: xcodebuild -project examples/example_apple_metal/example_apple_metal.xcodeproj -target example_apple_metal_macos

    - name: Build example_apple_opengl2
      run: xcodebuild -project examples/example_apple_opengl2/example_apple_opengl2.xcodeproj -target example_osx_opengl2

  iOS:
    runs-on: macos-latest
    steps:
    - uses: actions/checkout@v4

    - name: Build example_apple_metal
      run: |
        # Code signing is required, but we disable it because it is irrelevant for CI builds.
        xcodebuild -project examples/example_apple_metal/example_apple_metal.xcodeproj -target example_apple_metal_ios CODE_SIGN_IDENTITY="" CODE_SIGNING_REQUIRED=NO CODE_SIGNING_ALLOWED=NO

  Emscripten:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Install Dependencies
      run: |
        wget -q https://github.com/emscripten-core/emsdk/archive/master.tar.gz
        tar -xvf master.tar.gz
        emsdk-master/emsdk update
        emsdk-master/emsdk install latest
        emsdk-master/emsdk activate latest
        sudo apt-get install build-essential

    - name: Build example_sdl2_opengl3 with Emscripten
      run: |
        pushd emsdk-master
        source ./emsdk_env.sh
        popd
        make -C examples/example_sdl2_opengl3 -f Makefile.emscripten

    # This build compiles example_glfw_wgpu using Makefile.emscripten and Emscripten GLFW built-in implementation (-sUSE_GLFW=3)
    # This ensures 2 things: the make build works, and the GLFW built-in implementation is tested
    - name: Build example_glfw_wgpu with Emscripten/Makefile
      run: |
        pushd emsdk-master
        source ./emsdk_env.sh
        popd
        make -C examples/example_glfw_wgpu -f Makefile.emscripten

    # This build compiles example_glfw_wgpu using CMakeLists.txt and Emscripten GLFW contrib port (--use-port=contrib.glfw3)
    # This ensures 2 things: the CMake build works, and the GLFW contrib port is tested
    - name: Build example_glfw_wgpu with Emscripten/CMake
      run: |
        pushd emsdk-master
        source ./emsdk_env.sh
        popd
        emcc -v
        emcmake cmake -B build -DCMAKE_BUILD_TYPE=Release examples/example_glfw_wgpu
        cmake --build build

  Android:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Build example_android_opengl3
      run: |
        cd examples/example_android_opengl3/android
        gradle assembleDebug --stacktrace
