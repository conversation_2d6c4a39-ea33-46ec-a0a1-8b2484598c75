#pragma once
#include "pch.h"
#include "memory.h"
#include <d3d11.h>
#include <dxgi.h>

// Forward declarations para ImGui (incluir nos .cpp)
struct ImDrawList;
struct ImFont;
struct ImVec2;
struct ImVec4;

class Render
{
private:
    // DirectX 11
    ID3D11Device* m_pd3dDevice;
    ID3D11DeviceContext* m_pd3dDeviceContext;
    IDXGISwapChain* m_pSwapChain;
    ID3D11RenderTargetView* m_pMainRenderTargetView;
    
    // ImGui
    ImFont* m_pFont;
    bool m_bInitialized;
    
    // Informações da tela
    int m_iScreenWidth;
    int m_iScreenHeight;
    
    // Hook do Present
    static HRESULT __stdcall hkPresent(IDXGISwapChain* pSwapChain, UINT SyncInterval, UINT Flags);
    static LRESULT __stdcall hkWndProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
    
    // <PERSON>ais
    typedef HRESULT(__stdcall* Present_t)(IDXGISwapChain*, UINT, UINT);
    typedef LRESULT(__stdcall* WndProc_t)(HWND, UINT, WPARAM, LPARAM);
    
    static Present_t oPresent;
    static WndProc_t oWndProc;
    static Render* s_pInstance;
    
    HWND m_hWnd;
    
public:
    Render();
    ~Render();
    
    bool Initialize();
    void Shutdown();
    
    // Getters
    int GetScreenWidth() const { return m_iScreenWidth; }
    int GetScreenHeight() const { return m_iScreenHeight; }
    bool IsInitialized() const { return m_bInitialized; }
    
    // Funções de desenho
    void BeginFrame();
    void EndFrame();
    
    void DrawLine(const Vector2& from, const Vector2& to, uint32_t color, float thickness = 1.0f);
    void DrawRect(const Vector2& pos, const Vector2& size, uint32_t color, float thickness = 1.0f);
    void DrawFilledRect(const Vector2& pos, const Vector2& size, uint32_t color);
    void DrawText(const Vector2& pos, const char* text, uint32_t color, float fontSize = 14.0f);
    void DrawCircle(const Vector2& center, float radius, uint32_t color, int segments = 32);
    void DrawFilledCircle(const Vector2& center, float radius, uint32_t color, int segments = 32);
    
    // ESP específico
    void DrawPlayerBox(const Vector2& head, const Vector2& feet, uint32_t color, float thickness = 2.0f);
    void DrawHealthBar(const Vector2& pos, const Vector2& size, int health, int maxHealth);
    void DrawPlayerInfo(const Vector2& pos, const char* name, int health, int distance, uint32_t color);
    
    // Utilitários de cor
    static uint32_t ColorToUInt32(float r, float g, float b, float a = 1.0f);
    static uint32_t ColorToUInt32(const float color[4]);

    // Utilitários de texto
    Vector2 GetTextSize(const char* text, float fontSize = 14.0f);
    void DrawTextCentered(const Vector2& pos, const char* text, uint32_t color, float fontSize = 14.0f);
    void DrawTextWithOutline(const Vector2& pos, const char* text, uint32_t textColor, uint32_t outlineColor, float fontSize = 14.0f);
    
private:
    bool InitializeDirectX();
    bool InitializeImGui();
    void CleanupDirectX();
    void CleanupImGui();
    bool HookDirectX();
    void UnhookDirectX();
    
    void CreateRenderTarget();
    void CleanupRenderTarget();
    
    // Callback do Present
    void OnPresent();
};
