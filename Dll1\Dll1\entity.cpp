#include "pch.h"
#include "entity.h"

Entity::Entity() : m_p<PERSON><PERSON><PERSON>(nullptr), m_dw<PERSON><PERSON><PERSON>(0), m_dwController(0)
{
}

Entity::Entity(Memory* memory, uintptr_t address) : m_pM<PERSON><PERSON>(memory), m_dw<PERSON><PERSON><PERSON>(address), m_dwController(0)
{
}

Entity::~Entity()
{
}

bool Entity::IsValid() const
{
    if (!m_pMemory || !m_dwAddress)
        return false;
    
    return m_pMemory->IsValidAddress(m_dwAddress);
}

bool Entity::IsAlive() const
{
    if (!IsValid())
        return false;
    
    int lifeState = GetLifeState();
    return lifeState == Offsets::LifeState::LIFE_ALIVE;
}

bool Entity::IsPlayer() const
{
    if (!IsValid())
        return false;
    
    // Verifica se tem um controller válido
    return m_dwController != 0 && m_pMemory->IsValidAddress(m_dwController);
}

bool Entity::IsDormant() const
{
    if (!IsValid())
        return true;
    
    // Em CS2, verificamos se a entidade está ativa
    uintptr_t sceneNode = GetGameSceneNode();
    if (!sceneNode)
        return true;
    
    return false;
}

int Entity::GetHealth() const
{
    if (!IsValid())
        return 0;
    
    return m_pMemory->Read<int>(m_dwAddress + Offsets::Entity::m_iHealth);
}

int Entity::GetMaxHealth() const
{
    return 100; // CS2 padrão
}

int Entity::GetTeam() const
{
    if (!IsValid())
        return Offsets::Teams::TEAM_NONE;
    
    return m_pMemory->Read<int>(m_dwAddress + Offsets::Entity::m_iTeamNum);
}

int Entity::GetLifeState() const
{
    if (!IsValid())
        return Offsets::LifeState::LIFE_DEAD;
    
    return m_pMemory->Read<int>(m_dwAddress + Offsets::Entity::m_lifeState);
}

int Entity::GetFlags() const
{
    if (!IsValid())
        return 0;
    
    return m_pMemory->Read<int>(m_dwAddress + Offsets::Entity::m_fFlags);
}

int Entity::GetArmor() const
{
    if (!IsValid())
        return 0;
    
    return m_pMemory->Read<int>(m_dwAddress + Offsets::Entity::m_ArmorValue);
}

Vector3 Entity::GetPosition() const
{
    if (!IsValid())
        return Vector3();
    
    uintptr_t sceneNode = GetGameSceneNode();
    if (!sceneNode)
        return Vector3();
    
    return m_pMemory->Read<Vector3>(sceneNode + Offsets::Entity::m_vecOrigin);
}

Vector3 Entity::GetAbsOrigin() const
{
    if (!IsValid())
        return Vector3();
    
    uintptr_t sceneNode = GetGameSceneNode();
    if (!sceneNode)
        return Vector3();
    
    return m_pMemory->Read<Vector3>(sceneNode + Offsets::Entity::m_vecAbsOrigin);
}

Vector3 Entity::GetHeadPosition() const
{
    Vector3 pos = GetAbsOrigin();
    pos.z += 75.0f; // Altura aproximada da cabeça
    return pos;
}

std::string Entity::GetName() const
{
    if (!m_dwController || !m_pMemory->IsValidAddress(m_dwController))
        return "Unknown";
    
    return m_pMemory->ReadString(m_dwController + Offsets::Entity::m_sSanitizedPlayerName, 64);
}

bool Entity::GetPawnIsAlive() const
{
    if (!m_dwController)
        return false;
    
    return m_pMemory->Read<bool>(m_dwController + Offsets::Entity::m_bPawnIsAlive);
}

int Entity::GetPawnHealth() const
{
    if (!m_dwController)
        return 0;
    
    return m_pMemory->Read<int>(m_dwController + Offsets::Entity::m_iPawnHealth);
}

uintptr_t Entity::GetPlayerPawn() const
{
    if (!m_dwController)
        return 0;
    
    return m_pMemory->Read<uintptr_t>(m_dwController + Offsets::Entity::m_hPlayerPawn) & 0x7FFFFFFF;
}

float Entity::GetDistanceFrom(const Entity& other) const
{
    Vector3 myPos = GetAbsOrigin();
    Vector3 otherPos = other.GetAbsOrigin();
    return myPos.Distance(otherPos);
}

bool Entity::IsEnemy(const Entity& localPlayer) const
{
    if (!IsValid() || !localPlayer.IsValid())
        return false;
    
    int myTeam = GetTeam();
    int localTeam = localPlayer.GetTeam();
    
    return myTeam != localTeam && myTeam > Offsets::Teams::TEAM_SPECTATOR && localTeam > Offsets::Teams::TEAM_SPECTATOR;
}

bool Entity::IsOnGround() const
{
    return (GetFlags() & Offsets::Flags::FL_ONGROUND) != 0;
}

bool Entity::IsDucking() const
{
    return (GetFlags() & Offsets::Flags::FL_DUCKING) != 0;
}

bool Entity::IsScoped() const
{
    if (!IsValid())
        return false;
    
    return m_pMemory->Read<bool>(m_dwAddress + Offsets::Entity::m_bIsScoped);
}

bool Entity::HasDefuser() const
{
    if (!IsValid())
        return false;
    
    return m_pMemory->Read<bool>(m_dwAddress + Offsets::Entity::m_bHasDefuser);
}

void Entity::Update()
{
    // Atualização pode ser implementada se necessário
}

void Entity::SetController(uintptr_t controller)
{
    m_dwController = controller;
}

uintptr_t Entity::GetGameSceneNode() const
{
    if (!IsValid())
        return 0;
    
    return m_pMemory->Read<uintptr_t>(m_dwAddress + Offsets::Entity::m_pGameSceneNode);
}
