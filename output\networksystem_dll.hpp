// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-06 15:56:26.663360300 UTC

#pragma once

#include <cstddef>

namespace cs2_dumper {
    namespace schemas {
        // Module: networksystem.dll
        // Class count: 1
        // Enum count: 0
        namespace networksystem_dll {
            // Parent: None
            // Field count: 1
            namespace ChangeAccessorFieldPathIndex_t {
                constexpr std::ptrdiff_t m_Value = 0x0; // int32
            }
        }
    }
}
