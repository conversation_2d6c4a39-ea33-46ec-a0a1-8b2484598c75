#pragma once
#include "pch.h"
#include "memory.h"

// Classe para encontrar offsets de estruturas dinamicamente
class OffsetFinder
{
private:
    Memory* m_pMemory;
    uintptr_t m_localController;
    uintptr_t m_localPawn;
    
public:
    OffsetFinder(Memory* memory, uintptr_t localController, uintptr_t localPawn);
    ~OffsetFinder();
    
    // Encontrar offsets das estruturas
    uintptr_t FindPlayerPawnOffset();
    uintptr_t FindPawnHealthOffset();
    uintptr_t FindPawnAliveOffset();
    uintptr_t FindHealthOffset();
    uintptr_t FindTeamOffset();
    
    // Testar se um offset é válido
    bool TestPlayerPawnOffset(uintptr_t offset);
    bool TestHealthOffset(uintptr_t offset, uintptr_t pawn);
    bool TestTeamOffset(uintptr_t offset, uintptr_t pawn);
    
private:
    bool IsValidHandle(uintptr_t handle);
    bool IsValidHealth(int health);
    bool IsValidTeam(int team);
};
