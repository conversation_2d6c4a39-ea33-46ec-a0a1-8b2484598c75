// dllmain.cpp : Define o ponto de entrada para o aplicativo DLL.
#include "pch.h"
#include "esp.h"
#include <thread>

ESP* g_pESP = nullptr;

void MainThread(HMODULE hModule)
{
    // Aloca console para debug
    AllocConsole();
    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);

    printf("[ESP] Iniciando ESP para CS2...\n");

    // Inicializa ESP
    g_pESP = new ESP();
    if (!g_pESP->Initialize())
    {
        printf("[ESP] Falha ao inicializar ESP!\n");
        delete g_pESP;
        FreeConsole();
        FreeLibraryAndExitThread(hModule, 0);
        return;
    }

    printf("[ESP] ESP inicializado com sucesso!\n");

    // Loop principal
    while (!GetAsyncKeyState(VK_END))
    {
        g_pESP->Update();
        Sleep(1);
    }

    // Cleanup
    printf("[ESP] Finalizando ESP...\n");
    g_pESP->Shutdown();
    delete g_pESP;

    FreeConsole();
    FreeLibraryAndExitThread(hModule, 0);
}

BOOL APIENTRY DllMain( HMODULE hModule,
                       DWORD  ul_reason_for_call,
                       LPVOID lpReserved
                     )
{
    switch (ul_reason_for_call)
    {
    case DLL_PROCESS_ATTACH:
        DisableThreadLibraryCalls(hModule);
        CreateThread(nullptr, 0, (LPTHREAD_START_ROUTINE)MainThread, hModule, 0, nullptr);
        break;
    case DLL_THREAD_ATTACH:
    case DLL_THREAD_DETACH:
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
}

