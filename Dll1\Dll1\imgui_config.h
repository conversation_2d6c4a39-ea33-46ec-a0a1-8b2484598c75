#pragma once

// Configuração personalizada do ImGui para o projeto ESP
// Este arquivo deve ser incluído antes do imgui.h

// Definir configurações específicas do ImGui
#define IMGUI_DISABLE_OBSOLETE_FUNCTIONS
#define IMGUI_DISABLE_OBSOLETE_KEYIO

// Configurações de performance
#define IMGUI_DISABLE_DEMO_WINDOWS
#define IMGUI_DISABLE_METRICS_WINDOW

// Configurações de renderização
#define IMGUI_ENABLE_FREETYPE

// Configurações de input
#define IMGUI_DISABLE_WIN32_DEFAULT_CLIPBOARD_FUNCTIONS
#define IMGUI_DISABLE_WIN32_DEFAULT_IME_FUNCTIONS
