#include "pch.h"
#include "memory.h"
#include <TlHelp32.h>
#include <Psapi.h>

Memory::Memory() : m_hProcess(nullptr), m_dwProcessId(0)
{
}

Memory::~Memory()
{
    Shutdown();
}

bool Memory::Initialize()
{
    m_dwProcessId = GetCurrentProcessId();
    m_hProcess = GetCurrentProcess();
    
    if (!m_hProcess)
    {
        printf("[Memory] Falha ao obter handle do processo!\n");
        return false;
    }
    
    printf("[Memory] Inicializado com sucesso! PID: %d\n", m_dwProcessId);
    return true;
}

void Memory::Shutdown()
{
    if (m_hProcess && m_hProcess != GetCurrentProcess())
    {
        CloseHandle(m_hProcess);
        m_hProcess = nullptr;
    }
}

std::string Memory::ReadString(uintptr_t address, size_t maxLength)
{
    if (!IsValidAddress(address))
        return "";
    
    char buffer[256] = {};
    size_t readLength = min(maxLength, sizeof(buffer) - 1);
    
    if (ReadProcessMemory(GetCurrentProcess(), (LPCVOID)address, buffer, readLength, nullptr))
    {
        buffer[readLength] = '\0';
        return std::string(buffer);
    }
    
    return "";
}

uintptr_t Memory::GetModuleBase(const char* moduleName)
{
    HMODULE hModule = GetModuleHandleA(moduleName);
    if (!hModule)
    {
        printf("[Memory] Falha ao obter módulo: %s\n", moduleName);
        return 0;
    }
    
    printf("[Memory] Módulo %s encontrado em: 0x%p\n", moduleName, hModule);
    return (uintptr_t)hModule;
}

bool Memory::IsValidAddress(uintptr_t address)
{
    if (address == 0)
        return false;
    
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery((LPCVOID)address, &mbi, sizeof(mbi)) == 0)
        return false;
    
    return (mbi.State == MEM_COMMIT) && 
           (mbi.Protect & (PAGE_READONLY | PAGE_READWRITE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE));
}

bool Memory::WorldToScreen(const Vector3& worldPos, Vector2& screenPos, const ViewMatrix& viewMatrix, int screenWidth, int screenHeight)
{
    // Multiplicação da matriz de view
    float w = viewMatrix.matrix[3][0] * worldPos.x + 
              viewMatrix.matrix[3][1] * worldPos.y + 
              viewMatrix.matrix[3][2] * worldPos.z + 
              viewMatrix.matrix[3][3];
    
    if (w < 0.001f)
        return false;
    
    float x = viewMatrix.matrix[0][0] * worldPos.x + 
              viewMatrix.matrix[0][1] * worldPos.y + 
              viewMatrix.matrix[0][2] * worldPos.z + 
              viewMatrix.matrix[0][3];
    
    float y = viewMatrix.matrix[1][0] * worldPos.x + 
              viewMatrix.matrix[1][1] * worldPos.y + 
              viewMatrix.matrix[1][2] * worldPos.z + 
              viewMatrix.matrix[1][3];
    
    // Conversão para coordenadas de tela
    screenPos.x = (screenWidth / 2.0f) + (screenWidth / 2.0f) * (x / w);
    screenPos.y = (screenHeight / 2.0f) - (screenHeight / 2.0f) * (y / w);
    
    return true;
}
