latest signatures
Code:
	update_global_vars::m_update_global_vars.hook(g_opcodes->scan(g_modules->m_modules.client_dll.get_name(), "48 8B 0D ? ? ? ? 4C 8D 05 ? ? ? ? 48 85 D2"), update_global_vars::hk_update_global_vars);
	frame_stage_notify::m_frame_stage_notify.hook(g_opcodes->scan(g_modules->m_modules.client_dll.get_name(), "48 89 5C 24 ? 56 48 83 EC ? 8B 05"), frame_stage_notify::hk_frame_stage_notify);
	override_view::m_override_view.hook(g_opcodes->scan(g_modules->m_modules.client_dll.get_name(), "48 89 5C 24 ? 48 89 6C 24 ? 48 89 74 24 ? 57 41 56 41 57 48 83 EC ? 48 8B FA E8"), override_view::hk_override_view);
 
	on_add_entity::m_on_add_entity.hook(g_opcodes->scan(g_modules->m_modules.client_dll.get_name(), "48 89 74 24 ? 57 48 83 EC ? 48 8B F9 41 8B C0 B9"), on_add_entity::hk_on_add_entity);
	on_remove_entity::m_on_remove_entity.hook(g_opcodes->scan(g_modules->m_modules.client_dll.get_name(), "48 89 74 24 ? 57 48 83 EC ? 48 8B F9 41 8B C0 25"), on_remove_entity::hk_on_remove_entity);
 
	on_level_init::m_on_level_init.hook(g_opcodes->scan(g_modules->m_modules.client_dll.get_name(), "48 89 5C 24 ? 56 48 83 EC ? 48 8B 0D ? ? ? ? 48 8B F2"), on_level_init::hk_on_level_init);
	on_level_shutdown::m_on_level_shutdown.hook(g_opcodes->scan(g_modules->m_modules.client_dll.get_name(), "48 83 EC ? 48 8B 0D ? ? ? ? 48 8D 15 ? ? ? ? 45 33 C9 45 33 C0 ? ? ? FF 50 ? 48 85 C0 74 ? 48 8B 0D ? ? ? ? 48 8B D0 ? ? ? 41 FF 50 ? 48 83 C4"), on_level_shutdown::hk_on_level_shutdown);
 
	update_sky_box::m_update_sky_box.hook(g_opcodes->scan(g_modules->m_modules.client_dll.get_name(), "48 8B C4 48 89 58 ? 48 89 70 ? 55 57 41 54 41 55"), update_sky_box::hk_update_sky_box);
 
	draw_light_scene::m_draw_light_scene.hook(g_opcodes->scan(g_modules->m_modules.scenesystem_dll.get_name(), "48 89 54 24 ? 53 41 56 41 57"), draw_light_scene::hk_draw_light_scene); /////
 
	update_aggregate_scene_object::m_update_aggregate_scene_object.hook(g_opcodes->scan(g_modules->m_modules.scenesystem_dll.get_name(), "48 89 5C 24 ? 48 89 6C 24 ? 56 57 41 54 41 56 41 57 48 83 EC ? 4C 8B F95"), update_aggregate_scene_object::hk_update_aggregate_scene_object); /////
	draw_aggregate_scene_object::m_draw_aggregate_scene_object.hook(g_opcodes->scan(g_modules->m_modules.scenesystem_dll.get_name(), "48 89 54 24 ? 55 57 41 55 48 8D AC 24 ? ? ? ? B8 ? ? ? ? E8 ? ? ? ? 48 2B E0 49 63 F9"), draw_aggregate_scene_object::hk_draw_aggregate_scene_object); /////
 
	update_post_processing::m_update_post_processing.hook(g_opcodes->scan(g_modules->m_modules.client_dll.get_name(), "48 89 5C 24 ? 57 48 83 EC ? 80 B9 ? ? ? ? ? 8B DA 48 8B F9"), update_post_processing::hk_update_post_processing);
 
	should_update_sequences::m_should_update_sequences.hook(g_opcodes->scan(g_modules->m_modules.animation_system.get_name(), "48 89 5C 24 ? 48 89 74 24 ? 57 48 83 EC 20 49 8B 40 48"), should_update_sequences::hk_should_update_sequences);
 
	//xref: 
	should_draw_legs::m_should_draw_legs.hook(g_opcodes->scan(g_modules->m_modules.client_dll.get_name(), "48 89 5C 24 ? 48 89 74 24 ? 48 89 7C 24 ? 55 41 54 41 55 41 56 41 57 48 8D AC 24 ? ? ? ? 48 81 EC ? ? ? ? ? ? ? 45 33 ED 4D 8B F1"), should_draw_legs::hk_should_draw_legs);
 
	mark_interp_latch_flags_dirty::m_mark_interp_latch_flags_dirty.hook(g_opcodes->scan(g_modules->m_modules.client_dll.get_name(), "85 D2 0F 84 ? ? ? ? 55 57 48 83 EC"), mark_interp_latch_flags_dirty::hk_mark_interp_latch_flags_dirty);
 
	draw_scope_overlay::m_draw_scope_overlay.hook(g_opcodes->scan(g_modules->m_modules.client_dll.get_name(), "4C 8B DC 53 56 57 48 83 EC"), draw_scope_overlay::hk_draw_scope_overlay);
 
	get_field_of_view::m_get_field_of_view.hook(g_opcodes->scan(g_modules->m_modules.client_dll.get_name(), "40 53 48 83 EC ? 48 8B D9 E8 ? ? ? ? 48 85 C0 74 ? 48 8B C8 48 83 C4"), 
get_field_of_view::hk_get_field_of_view);
 
	m_global_vars = *reinterpret_cast<i_global_vars**>( g_opcodes->scan_absolute( client_dll, xorstr_( "48 89 15 ? ? ? ? 48 89 42" ), 0x3 ) );
	CHECK( xorstr_( "Global Vars" ), m_global_vars );
 
	m_trace = *reinterpret_cast<i_trace**>( g_opcodes->scan_absolute( client_dll, xorstr_( "4C 8B 35 ? ? ? ? 24" ), 0x3 ) );
	CHECK( xorstr_( "Traces" ), m_global_vars );
 
	m_entity_system = *reinterpret_cast<i_entity_system**>( g_opcodes->scan_absolute( client_dll, xorstr_( "48 8B 0D ? ? ? ? 48 89 7C 24 ? 8B FA C1 EB" ), 0x3 ) );
	CHECK( xorstr_( "Entity" ), m_entity_system );
wow signature // is suspicious