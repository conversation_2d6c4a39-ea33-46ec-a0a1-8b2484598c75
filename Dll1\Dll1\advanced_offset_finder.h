#pragma once
#include "pch.h"
#include "memory.h"

// Sistema avançado de detecção de offsets para CS2
class AdvancedOffsetFinder {
private:
    Memory* m_memory;
    uintptr_t m_clientBase;
    uintptr_t m_entityList;
    uintptr_t m_localController;
    uintptr_t m_localPawn;
    
public:
    // Offsets encontrados dinamicamente
    struct DynamicOffsets {
        // Controller offsets
        uintptr_t m_hPlayerPawn;
        uintptr_t m_sSanitizedPlayerName;
        uintptr_t m_iPawnHealth;
        uintptr_t m_bPawnIsAlive;
        uintptr_t m_iTeamNum;
        
        // Pawn offsets
        uintptr_t m_iHealth;
        uintptr_t m_iTeamNum_Pawn;
        uintptr_t m_vecOrigin;
        uintptr_t m_vecAbsOrigin;
        uintptr_t m_pGameSceneNode;
        uintptr_t m_modelState;
        uintptr_t m_vecViewOffset;
        
        // Entity offsets
        uintptr_t m_pEntity;
        uintptr_t m_ListEntry;
        
        bool valid;
        
        DynamicOffsets() : m_hPlayerPawn(0), m_sSanitizedPlayerName(0), m_iPawnHealth(0),
                          m_bPawnIsAlive(0), m_iTeamNum(0), m_iHealth(0), m_iTeamNum_Pawn(0),
                          m_vecOrigin(0), m_vecAbsOrigin(0), m_pGameSceneNode(0), m_modelState(0),
                          m_vecViewOffset(0), m_pEntity(0), m_ListEntry(0), valid(false) {}
    } offsets;
    
public:
    AdvancedOffsetFinder(Memory* memory, uintptr_t clientBase);
    ~AdvancedOffsetFinder();
    
    bool Initialize(uintptr_t entityList, uintptr_t localController, uintptr_t localPawn);
    const DynamicOffsets& GetOffsets() const { return offsets; }
    
    // Métodos de busca avançada
    bool FindControllerOffsets();
    bool FindPawnOffsets();
    bool FindEntityOffsets();
    bool FindNameOffsets();
    bool FindPositionOffsets();
    
    // Validação de offsets
    bool ValidateOffset(uintptr_t baseAddr, uintptr_t offset, const char* expectedType);
    bool ValidateHealthOffset(uintptr_t baseAddr, uintptr_t offset);
    bool ValidateTeamOffset(uintptr_t baseAddr, uintptr_t offset);
    bool ValidatePositionOffset(uintptr_t baseAddr, uintptr_t offset);
    bool ValidateNameOffset(uintptr_t baseAddr, uintptr_t offset);
    
    // Utilitários
    bool IsValidPlayerName(const char* name, size_t maxLen = 32);
    bool IsValidPosition(const Vector3& pos);
    
private:
    // Análise de estruturas
    bool AnalyzeControllerStructure(uintptr_t controller);
    bool AnalyzePawnStructure(uintptr_t pawn);
    bool AnalyzeEntityStructure(uintptr_t entity);
    
    // Busca por assinaturas conhecidas
    uintptr_t FindBySignature(const char* signature, int offset = 0);
    
    // Validação cruzada
    bool CrossValidateOffsets();
};
