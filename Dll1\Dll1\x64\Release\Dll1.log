﻿  dllmain.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(36,27): error C2248: 'AdvancedOffsetFinder::DynamicOffsets': não é possível acessar private struct declarado na classe 'AdvancedOffsetFinder'
  (compilando o arquivo fonte '/dllmain.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.h(15,12):
      consulte a declaração de 'AdvancedOffsetFinder::DynamicOffsets'
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.h(6,7):
      consulte a declaração de 'AdvancedOffsetFinder'
  
  simple_esp.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(36,27): error C2248: 'AdvancedOffsetFinder::DynamicOffsets': não é possível acessar private struct declarado na classe 'AdvancedOffsetFinder'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.h(15,12):
      consulte a declaração de 'AdvancedOffsetFinder::DynamicOffsets'
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.h(6,7):
      consulte a declaração de 'AdvancedOffsetFinder'
  
  entity_manager.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(36,27): error C2248: 'AdvancedOffsetFinder::DynamicOffsets': não é possível acessar private struct declarado na classe 'AdvancedOffsetFinder'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.h(15,12):
      consulte a declaração de 'AdvancedOffsetFinder::DynamicOffsets'
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.h(6,7):
      consulte a declaração de 'AdvancedOffsetFinder'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(46,21): error C2039: ' FindOffsets': não é um membro de 'EntityManager'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(26,7):
      consulte a declaração de 'EntityManager'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(51,9): error C2065: 'm_localPawn': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(57,26): error C2065: 'm_memory': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(57,41): error C2062: tipo 'int' inesperado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(60,17): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(61,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(61,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(61,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(61,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(70,24): error C2065: 'm_memory': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(70,39): error C2062: tipo 'int' inesperado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(73,17): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(74,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(74,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(74,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(74,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(83,27): error C2065: 'm_memory': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(83,51): error C2065: 'm_localPawn': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(86,17): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(87,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(87,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(87,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(87,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(95,9): error C2065: 'm_localController': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(101,32): error C2065: 'm_memory': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(101,58): error C2065: 'm_localController': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(104,17): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(105,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(105,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(105,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(105,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(105,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 2 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(105,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(105,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(105,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(114,26): error C2065: 'm_memory': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(114,41): error C2062: tipo 'int' inesperado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(117,17): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(118,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(118,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(118,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(118,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(127,26): error C2065: 'm_memory': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(127,41): error C2062: tipo 'bool' inesperado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(130,17): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(131,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(131,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(131,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(131,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(137,5): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(144,10): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(252,59): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(253,57): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(289,39): error C2065: 'pos': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(303,71): error C2065: 'pos': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(303,78): error C2065: 'pos': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(303,85): error C2065: 'pos': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(302,28): warning C4473: 'printf' : não há argumentos suficientes transmitidos para a cadeia de caracteres de formato
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(302,28):
      espaços reservados e seus parâmetros esperam 7 argumentos variadic, mas 4 foram fornecidos
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(302,28):
      o argumento variadic 5 ausente é requerido pela cadeia de formato '%.1f'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(329,9): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(330,58): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(332,9): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(333,58): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(341,13): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(343,64): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(348,13): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(349,61): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(351,13): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(352,69): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(429,22): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(432,43): error C2065: 'offsets': identificador não declarado
  (compilando o arquivo fonte '/entity_manager.cpp')
  
  advanced_offset_finder.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(79,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(79,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(79,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(79,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(93,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(93,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(93,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(93,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 2 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(140,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(140,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(140,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(140,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(155,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(155,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(155,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(155,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(181,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(181,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(181,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(181,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(198,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(198,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(198,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(198,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(224,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(224,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(224,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(224,20):
      considere usar '%I64X' na cadeia de formato
  
