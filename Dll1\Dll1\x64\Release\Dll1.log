﻿  dllmain.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(6,8): error C2011: 'Vector3': redefinição do tipo 'struct'
  (compilando o arquivo fonte '/dllmain.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(6,8):
      consulte a declaração de 'Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(19,13): error C2079: 'PlayerInfo::position' usa struct 'Vector3' indefinido
  (compilando o arquivo fonte '/dllmain.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(91,59): error C2039: ' function': não é um membro de 'std'
  (compilando o arquivo fonte '/dllmain.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\chrono(45,1):
      consulte a declaração de 'std'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(91,59): error C2061: erro de sintaxe: identificador 'function'
  (compilando o arquivo fonte '/dllmain.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(92,53): error C2039: ' function': não é um membro de 'std'
  (compilando o arquivo fonte '/dllmain.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\chrono(45,1):
      consulte a declaração de 'std'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(92,53): error C2061: erro de sintaxe: identificador 'function'
  (compilando o arquivo fonte '/dllmain.cpp')
  
  simple_esp.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(6,8): error C2011: 'Vector3': redefinição do tipo 'struct'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(6,8):
      consulte a declaração de 'Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(19,13): error C2079: 'PlayerInfo::position' usa struct 'Vector3' indefinido
  (compilando o arquivo fonte '/simple_esp.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(91,59): error C2039: ' function': não é um membro de 'std'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\chrono(45,1):
      consulte a declaração de 'std'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(91,59): error C2061: erro de sintaxe: identificador 'function'
  (compilando o arquivo fonte '/simple_esp.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(92,53): error C2039: ' function': não é um membro de 'std'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\chrono(45,1):
      consulte a declaração de 'std'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(92,53): error C2061: erro de sintaxe: identificador 'function'
  (compilando o arquivo fonte '/simple_esp.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(262,28): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(262,28):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(262,28):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(262,28):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(262,28): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 2 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(262,28):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(262,28):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(262,28):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(278,28): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(278,28):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(278,28):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(278,28):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(294,28): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(294,28):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(294,28):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(294,28):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(306,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 2 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(306,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(306,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(306,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(320,72): error C2065: 'correctHealthOffset': identificador não declarado
  (compilando o arquivo fonte '/simple_esp.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(321,70): error C2065: 'correctTeamOffset': identificador não declarado
  (compilando o arquivo fonte '/simple_esp.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(409,54): error C2065: 'correctHealthOffset': identificador não declarado
  (compilando o arquivo fonte '/simple_esp.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(410,52): error C2065: 'correctTeamOffset': identificador não declarado
  (compilando o arquivo fonte '/simple_esp.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(416,59): error C2065: 'correctHealthOffset': identificador não declarado
  (compilando o arquivo fonte '/simple_esp.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(416,80): error C2065: 'correctTeamOffset': identificador não declarado
  (compilando o arquivo fonte '/simple_esp.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(442,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(442,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(442,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(442,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(469,16): warning C4473: 'printf' : não há argumentos suficientes transmitidos para a cadeia de caracteres de formato
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(469,16):
      espaços reservados e seus parâmetros esperam 3 argumentos variadic, mas 0 foram fornecidos
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(469,16):
      o argumento variadic 1 ausente é requerido pela cadeia de formato '%.1f'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(485,16): warning C4473: 'printf' : não há argumentos suficientes transmitidos para a cadeia de caracteres de formato
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(485,16):
      espaços reservados e seus parâmetros esperam 3 argumentos variadic, mas 0 foram fornecidos
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(485,16):
      o argumento variadic 1 ausente é requerido pela cadeia de formato '%.1f'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(498,17): error C2039: ' PrintEntityInfo': não é um membro de 'SimpleESP'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.h(8,7):
      consulte a declaração de 'SimpleESP'
  
  entity_manager.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(6,8): error C2011: 'Vector3': redefinição do tipo 'struct'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(6,8):
      consulte a declaração de 'Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(19,13): error C2079: 'PlayerInfo::position' usa struct 'Vector3' indefinido
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(91,59): error C2039: ' function': não é um membro de 'std'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\chrono(45,1):
      consulte a declaração de 'std'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(91,59): error C2061: erro de sintaxe: identificador 'function'
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(92,53): error C2039: ' function': não é um membro de 'std'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\chrono(45,1):
      consulte a declaração de 'std'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(92,53): error C2061: erro de sintaxe: identificador 'function'
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(50,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(50,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(50,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(50,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(63,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(63,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(63,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(63,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(72,21): error C2079: 'pos' usa struct 'Vector3' indefinido
  (compilando o arquivo fonte '/entity_manager.cpp')
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(72,37): error C2672: 'Memory::Read': nenhuma função sobrecarregada correspondente encontrada
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(62,7):
      poderia ser 'T Memory::Read(uintptr_t)'
          C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(72,37):
          Falha ao especializar o modelo de função 'T Memory::Read(uintptr_t)'
              C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(72,37):
              Com os seguintes argumentos de template:
                  C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(72,37):
                  'T=Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24): warning C4473: 'printf' : não há argumentos suficientes transmitidos para a cadeia de caracteres de formato
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24):
      espaços reservados e seus parâmetros esperam 4 argumentos variadic, mas 1 foram fornecidos
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24):
      o argumento variadic 2 ausente é requerido pela cadeia de formato '%.1f'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 2 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(107,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(107,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(107,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(107,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(120,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(120,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(120,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(120,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(162,44): error C2672: 'Memory::Read': nenhuma função sobrecarregada correspondente encontrada
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(62,7):
      poderia ser 'T Memory::Read(uintptr_t)'
          C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(162,44):
          Falha ao especializar o modelo de função 'T Memory::Read(uintptr_t)'
              C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(162,44):
              Com os seguintes argumentos de template:
                  C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(162,44):
                  'T=Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(186,35): error C2664: 'float EntityManager::CalculateDistance(const Vector3 &,const Vector3 &) const': não é possível converter um argumento 1 de '<error type>' em 'const Vector3 &'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(186,66):
      Razão: não é possível converter de '<error type>' para 'const Vector3'
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(186,66):
      uso do tipo indefinido 'Vector3'
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(6,8):
      consulte a declaração de 'Vector3'
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(78,11):
      consulte a declaração de 'EntityManager::CalculateDistance'
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(186,35):
      ao tentar corresponder a lista de argumentos '(<error type>, <error type>)'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(227,41): error C2672: 'Memory::Read': nenhuma função sobrecarregada correspondente encontrada
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(62,7):
      poderia ser 'T Memory::Read(uintptr_t)'
          C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(227,41):
          Falha ao especializar o modelo de função 'T Memory::Read(uintptr_t)'
              C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(227,41):
              Com os seguintes argumentos de template:
                  C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(227,41):
                  'T=Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(277,24): error C2027: uso de tipo indefinido 'Vector3'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(6,8):
      consulte a declaração de 'Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(280,23): error C2027: uso de tipo indefinido 'Vector3'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(6,8):
      consulte a declaração de 'Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(282,22): error C2672: 'Memory::Read': nenhuma função sobrecarregada correspondente encontrada
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(62,7):
      poderia ser 'T Memory::Read(uintptr_t)'
          C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(282,22):
          Falha ao especializar o modelo de função 'T Memory::Read(uintptr_t)'
              C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(282,22):
              Com os seguintes argumentos de template:
                  C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(282,22):
                  'T=Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(324,16): error C2027: uso de tipo indefinido 'Vector3'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(6,8):
      consulte a declaração de 'Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(324,25): error C2027: uso de tipo indefinido 'Vector3'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(6,8):
      consulte a declaração de 'Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(325,16): error C2027: uso de tipo indefinido 'Vector3'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(6,8):
      consulte a declaração de 'Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(325,25): error C2027: uso de tipo indefinido 'Vector3'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(6,8):
      consulte a declaração de 'Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(326,16): error C2027: uso de tipo indefinido 'Vector3'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(6,8):
      consulte a declaração de 'Vector3'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(326,25): error C2027: uso de tipo indefinido 'Vector3'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\memory.h(6,8):
      consulte a declaração de 'Vector3'
  
