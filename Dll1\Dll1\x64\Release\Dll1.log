﻿  entity_manager.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(50,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(50,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(50,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(50,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(63,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(63,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(63,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(63,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 2 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(107,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(107,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(107,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(107,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(120,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(120,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(120,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(120,24):
      considere usar '%I64X' na cadeia de formato
  
  simple_esp.cpp
  Gerando código
  6 of 199 functions ( 3.0%) were compiled, the rest were copied from previous compilation.
    2 functions were new in current compilation
    14 functions had inline decision re-evaluated but remain unchanged
  Finalizada a geração de código
  Dll1.vcxproj -> C:\Users\<USER>\Desktop\dll cs2\Dll1\x64\Release\Dll1.dll
