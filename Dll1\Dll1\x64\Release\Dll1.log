﻿  pch.cpp
  dllmain.cpp
  memory.cpp
  simple_esp.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(212,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(212,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(212,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(212,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(225,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(225,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(225,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(225,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(232,16): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(232,16):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(232,16):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(232,16):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(232,16): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 2 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(232,16):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(232,16):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(232,16):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(282,28): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(282,28):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(282,28):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(282,28):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(282,28): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 2 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(282,28):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(282,28):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(282,28):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(298,28): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(298,28):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(298,28):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(298,28):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(314,28): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(314,28):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(314,28):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(314,28):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(326,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 2 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(326,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(326,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(326,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(360,36): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(360,36):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(360,36):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(360,36):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(432,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 4 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(432,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(432,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(432,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(432,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 5 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(432,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(432,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(432,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(457,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(457,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(457,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(457,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(464,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 3 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(464,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(464,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(464,20):
      considere usar '%I64X' na cadeia de formato
  
  pattern_scanner.cpp
  offset_finder.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(34,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(34,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(34,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(34,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(64,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(64,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(64,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(64,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(89,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(89,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(89,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(89,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(116,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(116,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(116,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(116,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(142,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(142,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(142,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\offset_finder.cpp(142,20):
      considere usar '%I64X' na cadeia de formato
  
  Gerando código
  Previous IPDB not found, fall back to full compilation.
  All 175 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finalizada a geração de código
  Dll1.vcxproj -> C:\Users\<USER>\Desktop\dll cs2\Dll1\x64\Release\Dll1.dll
