﻿  dllmain.cpp
  simple_esp.cpp
  entity_manager.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(50,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(50,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(50,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(50,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(63,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(63,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(63,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(63,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(76,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 2 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(94,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(107,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(107,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(107,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(107,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(120,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(120,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(120,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(120,24):
      considere usar '%I64X' na cadeia de formato
  
  Gerando código
  Compiler switch has changed, fall back to full compilation.
  All 197 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finalizada a geração de código
  Dll1.vcxproj -> C:\Users\<USER>\Desktop\dll cs2\Dll1\x64\Release\Dll1.dll
