﻿  pch.cpp
  dllmain.cpp
  memory.cpp
  simple_esp.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(261,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(261,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(261,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(261,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(268,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 3 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(268,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(268,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(268,20):
      considere usar '%I64X' na cadeia de formato
  
  pattern_scanner.cpp
  Gerando código
  Previous IPDB not found, fall back to full compilation.
  All 175 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finalizada a geração de código
  Dll1.vcxproj -> C:\Users\<USER>\Desktop\dll cs2\Dll1\x64\Release\Dll1.dll
