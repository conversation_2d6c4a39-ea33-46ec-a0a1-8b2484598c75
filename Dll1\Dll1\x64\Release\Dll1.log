﻿  pch.cpp
  dllmain.cpp
  esp.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\esp.cpp(228,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 2 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\esp.cpp(228,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\esp.cpp(228,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\esp.cpp(228,20):
      considere usar '%I64X' na cadeia de formato
  
  memory.cpp
  entity.cpp
  render.cpp
  menu.cpp
  simple_esp.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(159,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 3 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/simple_esp.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(159,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(159,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\simple_esp.cpp(159,24):
      considere usar '%I64X' na cadeia de formato
  
render.obj : error LNK2001: símbolo externo não resolvido MH_Initialize
render.obj : error LNK2001: símbolo externo não resolvido MH_Uninitialize
render.obj : error LNK2001: símbolo externo não resolvido "bool __cdecl ImGui::DebugCheckVersionAndDataLayout(char const *,unsigned __int64,unsigned __int64,unsigned __int64,unsigned __int64,unsigned __int64,unsigned __int64)" (?DebugCheckVersionAndDataLayout@ImGui@@YA_NPEBD_K11111@Z)
render.obj : error LNK2001: símbolo externo não resolvido "struct ImGuiContext * __cdecl ImGui::CreateContext(struct ImFontAtlas *)" (?CreateContext@ImGui@@YAPEAUImGuiContext@@PEAUImFontAtlas@@@Z)
render.obj : error LNK2001: símbolo externo não resolvido "struct ImGuiIO & __cdecl ImGui::GetIO(void)" (?GetIO@ImGui@@YAAEAUImGuiIO@@XZ)
render.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui::StyleColorsDark(struct ImGuiStyle *)" (?StyleColorsDark@ImGui@@YAXPEAUImGuiStyle@@@Z)
render.obj : error LNK2001: símbolo externo não resolvido "bool __cdecl ImGui_ImplWin32_Init(void *)" (?ImGui_ImplWin32_Init@@YA_NPEAX@Z)
render.obj : error LNK2001: símbolo externo não resolvido "bool __cdecl ImGui_ImplDX11_Init(struct ID3D11Device *,struct ID3D11DeviceContext *)" (?ImGui_ImplDX11_Init@@YA_NPEAUID3D11Device@@PEAUID3D11DeviceContext@@@Z)
render.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui_ImplDX11_Shutdown(void)" (?ImGui_ImplDX11_Shutdown@@YAXXZ)
render.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui_ImplWin32_Shutdown(void)" (?ImGui_ImplWin32_Shutdown@@YAXXZ)
render.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui::DestroyContext(struct ImGuiContext *)" (?DestroyContext@ImGui@@YAXPEAUImGuiContext@@@Z)
render.obj : error LNK2001: símbolo externo não resolvido MH_CreateHook
render.obj : error LNK2001: símbolo externo não resolvido MH_EnableHook
render.obj : error LNK2001: símbolo externo não resolvido MH_DisableHook
render.obj : error LNK2001: símbolo externo não resolvido "__int64 __cdecl ImGui_ImplWin32_WndProcHandler(struct HWND__ *,unsigned int,unsigned __int64,__int64)" (?ImGui_ImplWin32_WndProcHandler@@YA_JPEAUHWND__@@I_K_J@Z)
render.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui_ImplDX11_NewFrame(void)" (?ImGui_ImplDX11_NewFrame@@YAXXZ)
render.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui_ImplWin32_NewFrame(void)" (?ImGui_ImplWin32_NewFrame@@YAXXZ)
render.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui::NewFrame(void)" (?NewFrame@ImGui@@YAXXZ)
render.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui::Render(void)" (?Render@ImGui@@YAXXZ)
render.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui_ImplDX11_RenderDrawData(struct ImDrawData *)" (?ImGui_ImplDX11_RenderDrawData@@YAXPEAUImDrawData@@@Z)
render.obj : error LNK2001: símbolo externo não resolvido "struct ImDrawData * __cdecl ImGui::GetDrawData(void)" (?GetDrawData@ImGui@@YAPEAUImDrawData@@XZ)
render.obj : error LNK2001: símbolo externo não resolvido "struct ImDrawList * __cdecl ImGui::GetBackgroundDrawList(void)" (?GetBackgroundDrawList@ImGui@@YAPEAUImDrawList@@XZ)
render.obj : error LNK2001: símbolo externo não resolvido "public: void __cdecl ImDrawList::AddLine(struct ImVec2 const &,struct ImVec2 const &,unsigned int,float)" (?AddLine@ImDrawList@@QEAAXAEBUImVec2@@0IM@Z)
render.obj : error LNK2001: símbolo externo não resolvido "public: void __cdecl ImDrawList::AddRect(struct ImVec2 const &,struct ImVec2 const &,unsigned int,float,int,float)" (?AddRect@ImDrawList@@QEAAXAEBUImVec2@@0IMHM@Z)
render.obj : error LNK2001: símbolo externo não resolvido "public: void __cdecl ImDrawList::AddRectFilled(struct ImVec2 const &,struct ImVec2 const &,unsigned int,float,int)" (?AddRectFilled@ImDrawList@@QEAAXAEBUImVec2@@0IMH@Z)
render.obj : error LNK2001: símbolo externo não resolvido "struct ImFont * __cdecl ImGui::GetFont(void)" (?GetFont@ImGui@@YAPEAUImFont@@XZ)
render.obj : error LNK2001: símbolo externo não resolvido "public: void __cdecl ImDrawList::AddText(struct ImFont *,float,struct ImVec2 const &,unsigned int,char const *,char const *,float,struct ImVec4 const *)" (?AddText@ImDrawList@@QEAAXPEAUImFont@@MAEBUImVec2@@IPEBD2MPEBUImVec4@@@Z)
render.obj : error LNK2001: símbolo externo não resolvido "public: void __cdecl ImDrawList::AddText(struct ImVec2 const &,unsigned int,char const *,char const *)" (?AddText@ImDrawList@@QEAAXAEBUImVec2@@IPEBD1@Z)
render.obj : error LNK2001: símbolo externo não resolvido "public: void __cdecl ImDrawList::AddCircle(struct ImVec2 const &,float,unsigned int,int,float)" (?AddCircle@ImDrawList@@QEAAXAEBUImVec2@@MIHM@Z)
render.obj : error LNK2001: símbolo externo não resolvido "public: void __cdecl ImDrawList::AddCircleFilled(struct ImVec2 const &,float,unsigned int,int)" (?AddCircleFilled@ImDrawList@@QEAAXAEBUImVec2@@MIH@Z)
render.obj : error LNK2001: símbolo externo não resolvido "struct ImVec2 __cdecl ImGui::CalcTextSize(char const *,char const *,bool,float)" (?CalcTextSize@ImGui@@YA?AUImVec2@@PEBD0_NM@Z)
menu.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui::SetNextWindowPos(struct ImVec2 const &,int,struct ImVec2 const &)" (?SetNextWindowPos@ImGui@@YAXAEBUImVec2@@H0@Z)
menu.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui::SetNextWindowSize(struct ImVec2 const &,int)" (?SetNextWindowSize@ImGui@@YAXAEBUImVec2@@H@Z)
menu.obj : error LNK2001: símbolo externo não resolvido "bool __cdecl ImGui::Begin(char const *,bool *,int)" (?Begin@ImGui@@YA_NPEBDPEA_NH@Z)
menu.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui::Text(char const *,...)" (?Text@ImGui@@YAXPEBDZZ)
menu.obj : error LNK2001: símbolo externo não resolvido "bool __cdecl ImGui::Button(char const *,struct ImVec2 const &)" (?Button@ImGui@@YA_NPEBDAEBUImVec2@@@Z)
menu.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui::End(void)" (?End@ImGui@@YAXXZ)
menu.obj : error LNK2001: símbolo externo não resolvido "bool __cdecl ImGui::BeginTabBar(char const *,int)" (?BeginTabBar@ImGui@@YA_NPEBDH@Z)
menu.obj : error LNK2001: símbolo externo não resolvido "bool __cdecl ImGui::BeginTabItem(char const *,bool *,int)" (?BeginTabItem@ImGui@@YA_NPEBDPEA_NH@Z)
menu.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui::EndTabItem(void)" (?EndTabItem@ImGui@@YAXXZ)
menu.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui::EndTabBar(void)" (?EndTabBar@ImGui@@YAXXZ)
menu.obj : error LNK2001: símbolo externo não resolvido "class ESP * g_pESP" (?g_pESP@@3PEAVESP@@EA)
menu.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui::Separator(void)" (?Separator@ImGui@@YAXXZ)
menu.obj : error LNK2001: símbolo externo não resolvido "bool __cdecl ImGui::Checkbox(char const *,bool *)" (?Checkbox@ImGui@@YA_NPEBDPEA_N@Z)
menu.obj : error LNK2001: símbolo externo não resolvido "bool __cdecl ImGui::SliderFloat(char const *,float *,float,float,char const *,int)" (?SliderFloat@ImGui@@YA_NPEBDPEAMMM0H@Z)
menu.obj : error LNK2001: símbolo externo não resolvido "bool __cdecl ImGui::ColorEdit4(char const *,float * const,int)" (?ColorEdit4@ImGui@@YA_NPEBDQEAMH@Z)
menu.obj : error LNK2001: símbolo externo não resolvido "void __cdecl ImGui::BulletText(char const *,...)" (?BulletText@ImGui@@YAXPEBDZZ)
C:\Users\<USER>\Desktop\dll cs2\Dll1\x64\Release\Dll1.dll : fatal error LNK1120: 47 externo não resolvidos
