﻿  dllmain.cpp
  simple_esp.cpp
  entity_manager.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(46,21): error C2556: 'bool EntityManager::Update(void)': função sobrecarregada difere apenas pelo tipo de retorno de 'void EntityManager::Update(void)'
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(47,10):
      consulte a declaração de 'EntityManager::Update'
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.cpp(46,21): error C2371: 'EntityManager::Update': redefinição; tipos básicos diferentes
  (compilando o arquivo fonte '/entity_manager.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\entity_manager.h(47,10):
      consulte a declaração de 'EntityManager::Update'
  
  advanced_offset_finder.cpp
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(79,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(79,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(79,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(79,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(93,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(93,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(93,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(93,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 2 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(124,28):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(140,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(140,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(140,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(140,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(155,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(155,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(155,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(155,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(181,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(181,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(181,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(181,20):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(198,24): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(198,24):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(198,24):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(198,24):
      considere usar '%I64X' na cadeia de formato
  
C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(224,20): warning C4477: 'printf': a cadeia de formato '%X' requer um argumento do tipo 'unsigned int', mas um argumento variadic 1 tem o tipo 'uintptr_t'
  (compilando o arquivo fonte '/advanced_offset_finder.cpp')
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(224,20):
      considere usar '%llX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(224,20):
      considere usar '%IX' na cadeia de formato
      C:\Users\<USER>\Desktop\dll cs2\Dll1\Dll1\advanced_offset_finder.cpp(224,20):
      considere usar '%I64X' na cadeia de formato
  
