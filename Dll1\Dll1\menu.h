#pragma once
#include "pch.h"

class Menu
{
private:
    bool m_bShowMenu;
    bool m_bInitialized;
    
public:
    Menu();
    ~Menu();
    
    bool Initialize();
    void Shutdown();
    void Render();
    
    bool IsVisible() const { return m_bShowMenu; }
    void SetVisible(bool visible) { m_bShowMenu = visible; }
    void Toggle() { m_bShowMenu = !m_bShowMenu; }
    
private:
    void RenderMainWindow();
    void RenderESPSettings();
    void RenderMiscSettings();
};
