#include "pch.h"
#include "offset_finder.h"

OffsetFinder::OffsetFinder(Memory* memory, uintptr_t localController, uintptr_t localPawn)
    : m_pM<PERSON><PERSON>(memory), m_localController(localController), m_localPawn(localPawn)
{
}

OffsetFinder::~OffsetFinder()
{
}

uintptr_t OffsetFinder::FindPlayerPawnOffset()
{
    printf("[OffsetFinder] Procurando offset m_hPlayerPawn...\n");
    
    if (!m_localController || !m_localPawn)
    {
        printf("[OffsetFinder] LocalController ou LocalPawn inválidos!\n");
        return 0;
    }
    
    // Testar offsets conhecidos e próximos
    uintptr_t offsets[] = {
        0x824, 0x820, 0x828, 0x82C, 0x830, 0x834, 0x838, 0x83C,
        0x7FC, 0x800, 0x804, 0x808, 0x80C, 0x810, 0x814, 0x818,
        0x81C, 0x840, 0x844, 0x848, 0x84C, 0x850
    };
    
    for (int i = 0; i < 22; i++)
    {
        if (TestPlayerPawnOffset(offsets[i]))
        {
            printf("[OffsetFinder] m_hPlayerPawn encontrado: 0x%X\n", offsets[i]);
            return offsets[i];
        }
    }
    
    printf("[OffsetFinder] m_hPlayerPawn não encontrado!\n");
    return 0;
}

uintptr_t OffsetFinder::FindPawnHealthOffset()
{
    printf("[OffsetFinder] Procurando offset m_iPawnHealth...\n");
    
    if (!m_localController)
    {
        printf("[OffsetFinder] LocalController inválido!\n");
        return 0;
    }
    
    // Testar offsets próximos ao m_hPlayerPawn
    uintptr_t offsets[] = {
        0x830, 0x834, 0x838, 0x83C, 0x828, 0x82C, 0x840, 0x844,
        0x848, 0x84C, 0x850, 0x824, 0x820, 0x81C, 0x818
    };
    
    for (int i = 0; i < 15; i++)
    {
        int health = m_pMemory->Read<int>(m_localController + offsets[i]);
        if (IsValidHealth(health))
        {
            printf("[OffsetFinder] m_iPawnHealth encontrado: 0x%X (valor: %d)\n", offsets[i], health);
            return offsets[i];
        }
    }
    
    printf("[OffsetFinder] m_iPawnHealth não encontrado!\n");
    return 0;
}

uintptr_t OffsetFinder::FindPawnAliveOffset()
{
    printf("[OffsetFinder] Procurando offset m_bPawnIsAlive...\n");
    
    if (!m_localController)
        return 0;
    
    uintptr_t offsets[] = {
        0x82C, 0x828, 0x824, 0x830, 0x834, 0x838, 0x820, 0x81C
    };
    
    for (int i = 0; i < 8; i++)
    {
        bool alive = m_pMemory->Read<bool>(m_localController + offsets[i]);
        if (alive) // Se está vivo (assumindo que o jogador local está vivo)
        {
            printf("[OffsetFinder] m_bPawnIsAlive encontrado: 0x%X (valor: %s)\n", 
                   offsets[i], alive ? "true" : "false");
            return offsets[i];
        }
    }
    
    printf("[OffsetFinder] m_bPawnIsAlive não encontrado!\n");
    return 0;
}

uintptr_t OffsetFinder::FindHealthOffset()
{
    printf("[OffsetFinder] Procurando offset m_iHealth (pawn)...\n");
    
    if (!m_localPawn)
        return 0;
    
    uintptr_t offsets[] = {
        0x344, 0x340, 0x348, 0x34C, 0x350, 0x33C, 0x338, 0x354,
        0x358, 0x35C, 0x360, 0x330, 0x32C, 0x328
    };
    
    for (int i = 0; i < 14; i++)
    {
        if (TestHealthOffset(offsets[i], m_localPawn))
        {
            int health = m_pMemory->Read<int>(m_localPawn + offsets[i]);
            printf("[OffsetFinder] m_iHealth encontrado: 0x%X (valor: %d)\n", offsets[i], health);
            return offsets[i];
        }
    }
    
    printf("[OffsetFinder] m_iHealth não encontrado!\n");
    return 0;
}

uintptr_t OffsetFinder::FindTeamOffset()
{
    printf("[OffsetFinder] Procurando offset m_iTeamNum (pawn)...\n");
    
    if (!m_localPawn)
        return 0;
    
    uintptr_t offsets[] = {
        0x3E3, 0x3E0, 0x3E4, 0x3E8, 0x3EC, 0x3DC, 0x3D8, 0x3F0,
        0x3F4, 0x3F8, 0x3FC, 0x3D4, 0x3D0, 0x3CC
    };
    
    for (int i = 0; i < 14; i++)
    {
        if (TestTeamOffset(offsets[i], m_localPawn))
        {
            int team = m_pMemory->Read<int>(m_localPawn + offsets[i]);
            printf("[OffsetFinder] m_iTeamNum encontrado: 0x%X (valor: %d)\n", offsets[i], team);
            return offsets[i];
        }
    }
    
    printf("[OffsetFinder] m_iTeamNum não encontrado!\n");
    return 0;
}

bool OffsetFinder::TestPlayerPawnOffset(uintptr_t offset)
{
    uintptr_t handle = m_pMemory->Read<uintptr_t>(m_localController + offset);
    return IsValidHandle(handle);
}

bool OffsetFinder::TestHealthOffset(uintptr_t offset, uintptr_t pawn)
{
    int health = m_pMemory->Read<int>(pawn + offset);
    return IsValidHealth(health);
}

bool OffsetFinder::TestTeamOffset(uintptr_t offset, uintptr_t pawn)
{
    int team = m_pMemory->Read<int>(pawn + offset);
    return IsValidTeam(team);
}

bool OffsetFinder::IsValidHandle(uintptr_t handle)
{
    // Handles válidos do CS2 geralmente seguem o padrão 0x80000XXX
    return (handle != 0) && 
           (handle != 0x1E8) && 
           (handle != 0x2BD) && 
           (handle != 0x130) &&
           (handle < 0xFFFFFFFF) &&
           ((handle & 0x80000000) || (handle & 0x7FFFFFFF) < 2048);
}

bool OffsetFinder::IsValidHealth(int health)
{
    return (health > 0 && health <= 100);
}

bool OffsetFinder::IsValidTeam(int team)
{
    return (team == 2 || team == 3); // T ou CT
}
