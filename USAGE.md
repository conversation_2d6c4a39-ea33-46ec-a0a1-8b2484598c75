# Guia de Uso - CS2 ESP

## Compilação

### Método 1: Script Automático
1. Execute `build.bat`
2. Aguarde a compilação
3. A DLL estará em `Dll1\x64\Release\Dll1.dll`

### Método 2: Visual Studio
1. Abra `Dll1\Dll1.sln`
2. Selecione `Release` e `x64`
3. Build > Build Solution (Ctrl+Shift+B)

## Injeção da DLL

### Injetores Recomendados
- **Process Hacker** (gratuito)
- **Extreme Injector** (gratuito)
- **Xenos Injector** (gratuito)

### Passos para Injeção
1. Abra o CS2 e entre em um servidor/mapa
2. Abra o injetor de sua escolha
3. Selecione o processo `cs2.exe`
4. Selecione a DLL `Dll1.dll`
5. Clique em "Inject"
6. Verifique se apareceu um console com logs do ESP

## Controles

| Tecla | Função |
|-------|--------|
| `INSERT` | Abrir/fechar menu de configurações |
| `END` | Descarregar ESP (sair) |

## Configurações do ESP

### Aba ESP
- **Enable ESP**: Liga/desliga o ESP completamente
- **Show Boxes**: Mostra caixas ao redor dos jogadores
- **Show Health**: Mostra barras de vida
- **Show Names**: Mostra nomes dos jogadores
- **Show Distance**: Mostra distância em metros
- **Show Team**: Mostra aliados (padrão: apenas inimigos)
- **Max Distance**: Distância máxima para mostrar jogadores

### Cores
- **Enemy Color**: Cor para inimigos (padrão: vermelho)
- **Team Color**: Cor para aliados (padrão: verde)
- **Health Color**: Cor da barra de vida (padrão: verde)

## Troubleshooting

### ESP não aparece
1. Verifique se a DLL foi injetada corretamente
2. Verifique o console para mensagens de erro
3. Certifique-se de estar em um servidor/mapa ativo
4. Pressione INSERT para verificar se o menu abre

### Jogo crasha
1. **Offsets desatualizados**: CS2 foi atualizado, offsets podem estar incorretos
2. **Injeção incorreta**: Use um injetor diferente
3. **Antivírus**: Desative temporariamente o antivírus

### Menu não abre
1. Pressione INSERT várias vezes
2. Verifique se a DLL está carregada no Process Hacker
3. Reinicie o jogo e injete novamente

### Performance baixa
1. Diminua a distância máxima
2. Desative recursos desnecessários (nomes, distância)
3. Use apenas boxes para melhor performance

## Logs e Debug

### Console
O ESP abre automaticamente um console que mostra:
- Status de inicialização
- Módulos encontrados
- Offsets carregados
- Entidades detectadas
- Erros e avisos

### Mensagens Importantes
```
[ESP] Inicializando ESP...
[Memory] Módulo client.dll encontrado em: 0x...
[ESP] Entidades encontradas: X
[ESP] ESP inicializado com sucesso!
```

## Atualizações do CS2

Quando o CS2 é atualizado, os offsets podem ficar desatualizados. Para corrigir:

1. **Baixe novos offsets** do cs2-dumper
2. **Substitua os arquivos** em `output/`
3. **Atualize** `offsets.h` se necessário
4. **Recompile** o projeto

### Sinais de Offsets Desatualizados
- ESP não mostra jogadores
- Jogo crasha ao injetar
- Informações incorretas (vida, nomes)
- Console mostra erros de memória

## Segurança e VAC

### ⚠️ AVISOS IMPORTANTES
- **Use por sua conta e risco**
- **Pode resultar em ban VAC**
- **Teste apenas em servidores offline**
- **Não use em matchmaking competitivo**

### Medidas de Segurança
- O ESP não modifica arquivos do jogo
- Usa apenas leitura de memória
- Hooks seguros com MinHook
- Cleanup adequado ao sair

### Recomendações
1. Use uma conta secundária
2. Teste em servidores de treino
3. Não seja óbvio demais
4. Descarregue antes de fechar o jogo

## Recursos Avançados

### Personalização de Cores
No menu, você pode ajustar:
- RGB para cada tipo de jogador
- Transparência (Alpha)
- Cores diferentes para diferentes situações

### Filtros
- Mostrar apenas inimigos
- Filtrar por distância
- Filtrar por vida (apenas vivos)

### Performance
- O ESP é otimizado para 60+ FPS
- Usa DirectX 11 nativo do jogo
- Renderização eficiente com ImGui

## Suporte

### Problemas Comuns
1. **"Falha ao obter client.dll"**: CS2 não está rodando ou processo não encontrado
2. **"Falha ao inicializar DirectX"**: Problema com drivers gráficos
3. **"Entidades encontradas: 0"**: Offsets incorretos ou não está em jogo

### Informações para Debug
Ao reportar problemas, inclua:
- Versão do Windows
- Versão do CS2
- Mensagens do console
- Passos para reproduzir o problema

## Créditos e Licença

Desenvolvido por **Augment Agent** para fins educacionais.

**Bibliotecas utilizadas:**
- ImGui (interface)
- MinHook (hooking)
- cs2-dumper (offsets)

**Licença:** Apenas uso educacional
