#include "pch.h"
#include "menu.h"

// Incluir ImGui via wrapper
#include "imgui_wrapper.h"

extern class ESP* g_pESP;

Menu::Menu() : m_bShowMenu(false), m_bInitialized(false)
{
}

Menu::~Menu()
{
    Shutdown();
}

bool Menu::Initialize()
{
    m_bInitialized = true;
    return true;
}

void Menu::Shutdown()
{
    m_bInitialized = false;
}

void Menu::Render()
{
    if (!m_bInitialized || !m_bShowMenu)
        return;
    
    RenderMainWindow();
}

void Menu::RenderMainWindow()
{
    ImGui::SetNextWindowSize(ImVec2(400, 300), ImGuiCond_FirstUseEver);
    
    if (ImGui::Begin("CS2 ESP - by Augment Agent", &m_bShowMenu, ImGuiWindowFlags_NoCollapse))
    {
        if (ImGui::BeginTabBar("MainTabs"))
        {
            if (ImGui::BeginTabItem("ESP"))
            {
                RenderESPSettings();
                ImGui::EndTabItem();
            }
            
            if (ImGui::BeginTabItem("Misc"))
            {
                RenderMiscSettings();
                ImGui::EndTabItem();
            }
            
            ImGui::EndTabBar();
        }
    }
    ImGui::End();
}

void Menu::RenderESPSettings()
{
    if (!g_pESP)
        return;
    
    ImGui::Text("ESP Settings");
    ImGui::Separator();
    
    // Configurações básicas do ESP
    static bool espEnabled = true;
    static bool showBoxes = true;
    static bool showHealth = true;
    static bool showNames = true;
    static bool showDistance = true;
    static bool showTeam = false;
    static float maxDistance = 500.0f;
    
    ImGui::Checkbox("Enable ESP", &espEnabled);
    ImGui::Checkbox("Show Boxes", &showBoxes);
    ImGui::Checkbox("Show Health", &showHealth);
    ImGui::Checkbox("Show Names", &showNames);
    ImGui::Checkbox("Show Distance", &showDistance);
    ImGui::Checkbox("Show Team", &showTeam);
    
    ImGui::SliderFloat("Max Distance", &maxDistance, 50.0f, 1000.0f, "%.0f");
    
    ImGui::Separator();
    ImGui::Text("Colors");
    
    static float colorEnemy[4] = { 1.0f, 0.0f, 0.0f, 1.0f };
    static float colorTeam[4] = { 0.0f, 1.0f, 0.0f, 1.0f };
    static float colorHealth[4] = { 0.0f, 1.0f, 0.0f, 1.0f };
    
    ImGui::ColorEdit4("Enemy Color", colorEnemy);
    ImGui::ColorEdit4("Team Color", colorTeam);
    ImGui::ColorEdit4("Health Color", colorHealth);
    
    // Aplicar configurações (seria necessário adicionar getters/setters na classe ESP)
    // g_pESP->SetConfig(...);
}

void Menu::RenderMiscSettings()
{
    ImGui::Text("Miscellaneous Settings");
    ImGui::Separator();
    
    ImGui::Text("Controls:");
    ImGui::BulletText("INSERT - Toggle Menu");
    ImGui::BulletText("END - Unload ESP");
    
    ImGui::Separator();
    
    if (ImGui::Button("Unload ESP"))
    {
        // Sinalizar para descarregar
        PostMessage(GetConsoleWindow(), WM_KEYDOWN, VK_END, 0);
    }
    
    ImGui::Separator();
    ImGui::Text("ESP Status: Running");
    ImGui::Text("Build: Debug");
    ImGui::Text("Version: 1.0.0");
}
