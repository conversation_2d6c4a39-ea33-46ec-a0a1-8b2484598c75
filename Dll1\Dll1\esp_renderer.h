#pragma once
#include "pch.h"
#include "memory.h"
#include "entity_manager.h"
#include <d3d11.h>
#include <dxgi.h>

// Renderer para ESP visual
class ESPRenderer {
private:
    Memory* m_memory;
    EntityManager* m_entityManager;
    
    // DirectX 11
    ID3D11Device* m_device;
    ID3D11DeviceContext* m_context;
    IDXGISwapChain* m_swapChain;
    ID3D11RenderTargetView* m_renderTargetView;
    
    // Viewport info
    int m_screenWidth;
    int m_screenHeight;
    
    // ViewMatrix para world-to-screen
    float m_viewMatrix[16];
    uintptr_t m_viewMatrixAddress;
    
public:
    ESPRenderer(Memory* memory, EntityManager* entityManager);
    ~ESPRenderer();
    
    bool Initialize(uintptr_t viewMatrixAddr);
    void Shutdown();
    
    void Render();
    void RenderESP();
    
private:
    bool InitializeDirectX();
    void UpdateViewMatrix();
    bool WorldToScreen(const Vector3& worldPos, Vector2& screenPos);
    
    void DrawBox(const Vector2& topLeft, const Vector2& bottomRight, DWORD color);
    void DrawLine(const Vector2& start, const Vector2& end, DWORD color);
    void DrawText(const Vector2& pos, const char* text, DWORD color);
    
    void RenderPlayer(const PlayerInfo& player);
    void RenderLocalPlayer(const PlayerInfo& localPlayer);
    
    // Utility
    Vector2 GetBoundingBox(const Vector3& position, float height = 72.0f);
};

// Estrutura para posição 2D
struct Vector2 {
    float x, y;
    Vector2() : x(0), y(0) {}
    Vector2(float x, float y) : x(x), y(y) {}
};
