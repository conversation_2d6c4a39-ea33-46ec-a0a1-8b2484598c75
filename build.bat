@echo off
echo ================================
echo CS2 ESP - BUILD SCRIPT (SAFE VERSION)
echo ================================
echo.
echo Building CRASH-FREE version without ImGui/MinHook
echo This version uses only console output for debugging
echo.

cd "Dll1"

echo [1/4] Cleaning previous build...
if exist "x64\Release" rmdir /s /q "x64\Release"
if exist "x64\Debug" rmdir /s /q "x64\Debug"

echo [2/4] Building Safe Release x64...
msbuild Dll1.sln /p:Configuration=Release /p:Platform=x64 /p:RuntimeLibrary=MultiThreaded /m

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ================================
    echo BUILD SUCCESSFUL!
    echo ================================
    echo.
    echo Output: Dll1\x64\Release\Dll1.dll
    echo Size:
    dir "x64\Release\Dll1.dll" | find "Dll1.dll"
    echo.
    echo [3/4] Checking safe build...
    echo Static runtime: YES
    echo ImGui: DISABLED (safe mode)
    echo MinHook: DISABLED (safe mode)
    echo Hooks: NONE (crash-free)
    echo.
    echo [4/4] Ready for SAFE injection!
    echo.
    echo INJECTION STEPS:
    echo 1. Open CS2 and join a server/map
    echo 2. Run injector as ADMINISTRATOR
    echo 3. Inject Dll1.dll into cs2.exe
    echo 4. Look for console window (auto-opens)
    echo 5. Check entity detection in console
    echo 6. Press END to unload
    echo.
    echo TROUBLESHOOTING:
    echo - If injection fails, check INJECTION_TROUBLESHOOTING.md
    echo - Try different injectors (Process Hacker, Extreme Injector)
    echo - Make sure CS2 is x64 and DLL is x64
    echo - Run as administrator
    echo.
) else (
    echo.
    echo ================================
    echo BUILD FAILED!
    echo ================================
    echo.
    echo Check the error messages above.
    echo Common issues:
    echo - Missing Visual Studio components
    echo - Missing Windows SDK
    echo - Path issues with ImGui/MinHook
    echo.
)

pause
