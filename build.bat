@echo off
echo Building CS2 ESP...
echo.

cd "Dll1"

echo Cleaning previous build...
if exist "x64\Release" rmdir /s /q "x64\Release"
if exist "x64\Debug" rmdir /s /q "x64\Debug"

echo.
echo Building Release x64...
msbuild Dll1.sln /p:Configuration=Release /p:Platform=x64 /m

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ================================
    echo BUILD SUCCESSFUL!
    echo ================================
    echo.
    echo Output: Dll1\x64\Release\Dll1.dll
    echo.
    echo Usage:
    echo 1. Open CS2
    echo 2. Inject Dll1.dll into cs2.exe
    echo 3. Press INSERT to open menu
    echo 4. Press END to unload
    echo.
) else (
    echo.
    echo ================================
    echo BUILD FAILED!
    echo ================================
    echo.
    echo Check the error messages above.
)

pause
