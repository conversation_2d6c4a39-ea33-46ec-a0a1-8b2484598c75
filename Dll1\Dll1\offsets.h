#pragma once
#include <cstdint>

// Offsets atualizados para CS2 - 2025-07-06
namespace Offsets
{
    // Módulos
    namespace Modules
    {
        constexpr const char* CLIENT_DLL = "client.dll";
        constexpr const char* ENGINE2_DLL = "engine2.dll";
    }
    
    // Client.dll offsets
    namespace Client
    {
        constexpr uintptr_t dwEntityList = 0x1A044C0;
        constexpr uintptr_t dwGameEntitySystem = 0x1B27B48;
        constexpr uintptr_t dwGameEntitySystem_highestEntityIndex = 0x20F0;
        constexpr uintptr_t dwLocalPlayerController = 0x1A52D00;
        constexpr uintptr_t dwLocalPlayerPawn = 0x18580D0;
        constexpr uintptr_t dwViewMatrix = 0x1A6D260;
        constexpr uintptr_t dwViewRender = 0x1A6DB70;
        constexpr uintptr_t dwGlobalVars = 0x184BEB0;
    }
    
    // Engine2.dll offsets
    namespace Engine
    {
        constexpr uintptr_t dwWindowWidth = 0x623598;
        constexpr uintptr_t dwWindowHeight = 0x62359C;
    }
    
    // Entity offsets
    namespace Entity
    {
        // C_BaseEntity
        constexpr uintptr_t m_pGameSceneNode = 0x328;
        constexpr uintptr_t m_iHealth = 0x344;
        constexpr uintptr_t m_lifeState = 0x348;
        constexpr uintptr_t m_iTeamNum = 0x3E3;
        constexpr uintptr_t m_fFlags = 0x3EC;
        
        // CGameSceneNode
        constexpr uintptr_t m_vecAbsOrigin = 0xD0;
        constexpr uintptr_t m_vecOrigin = 0x88;
        
        // CCSPlayerController
        constexpr uintptr_t m_hPlayerPawn = 0x824;
        constexpr uintptr_t m_bPawnIsAlive = 0x82C;
        constexpr uintptr_t m_iPawnHealth = 0x830;
        constexpr uintptr_t m_sSanitizedPlayerName = 0x770;
        
        // C_CSPlayerPawn
        constexpr uintptr_t m_ArmorValue = 0x23F4;
        constexpr uintptr_t m_bHasDefuser = 0x1504;
        constexpr uintptr_t m_bIsScoped = 0x23E8;
        
        // C_CSPlayerPawnBase
        constexpr uintptr_t m_hOriginalController = 0x1424;
    }
    
    // Flags
    namespace Flags
    {
        constexpr int FL_ONGROUND = (1 << 0);
        constexpr int FL_DUCKING = (1 << 1);
        constexpr int FL_WATERJUMP = (1 << 2);
        constexpr int FL_ONTRAIN = (1 << 3);
        constexpr int FL_INRAIN = (1 << 4);
        constexpr int FL_FROZEN = (1 << 5);
        constexpr int FL_ATCONTROLS = (1 << 6);
        constexpr int FL_CLIENT = (1 << 7);
        constexpr int FL_FAKECLIENT = (1 << 8);
    }
    
    // Teams
    namespace Teams
    {
        constexpr int TEAM_NONE = 0;
        constexpr int TEAM_SPECTATOR = 1;
        constexpr int TEAM_T = 2;
        constexpr int TEAM_CT = 3;
    }
    
    // Life states
    namespace LifeState
    {
        constexpr int LIFE_ALIVE = 0;
        constexpr int LIFE_DYING = 1;
        constexpr int LIFE_DEAD = 2;
        constexpr int LIFE_RESPAWNABLE = 3;
        constexpr int LIFE_DISCARDBODY = 4;
    }
}
