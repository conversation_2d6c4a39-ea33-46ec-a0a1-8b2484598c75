#pragma once
#include "memory.h"
#include "offsets.h"
#include <string>

class Entity
{
private:
    Memory* m_pMemory;
    uintptr_t m_dwAddress;
    uintptr_t m_dwController;
    
public:
    Entity();
    Entity(Memory* memory, uintptr_t address);
    ~Entity();
    
    // Getters básicos
    uintptr_t GetAddress() const { return m_dwAddress; }
    uintptr_t GetController() const { return m_dwController; }
    
    // Informações da entidade
    bool IsValid() const;
    bool IsAlive() const;
    bool IsPlayer() const;
    bool IsDormant() const;
    
    // Propriedades do jogador
    int GetHealth() const;
    int GetMaxHealth() const;
    int GetTeam() const;
    int GetLifeState() const;
    int GetFlags() const;
    int GetArmor() const;
    
    // Posição
    Vector3 GetPosition() const;
    Vector3 GetAbsOrigin() const;
    Vector3 GetHeadPosition() const;
    
    // Informações do controller
    std::string GetName() const;
    bool GetPawnIsAlive() const;
    int GetPawnHealth() const;
    uintptr_t GetPlayerPawn() const;
    
    // Utilitários
    float GetDistanceFrom(const Entity& other) const;
    bool IsEnemy(const Entity& localPlayer) const;
    bool IsOnGround() const;
    bool IsDucking() const;
    bool IsScoped() const;
    bool HasDefuser() const;
    
    // Atualização
    void Update();
    void SetController(uintptr_t controller);
    
private:
    uintptr_t GetGameSceneNode() const;
};
