#include "pch.h"
#include "advanced_offset_finder.h"

AdvancedOffsetFinder::AdvancedOffsetFinder(Memory* memory, uintptr_t clientBase)
    : m_memory(memory), m_clientBase(clientBase), m_entityList(0), m_localController(0), m_localPawn(0)
{
}

AdvancedOffsetFinder::~AdvancedOffsetFinder()
{
}

bool AdvancedOffsetFinder::Initialize(uintptr_t entityList, uintptr_t localController, uintptr_t localPawn)
{
    m_entityList = entityList;
    m_localController = localController;
    m_localPawn = localPawn;
    
    printf("[AdvancedOffsetFinder] === ANÁLISE AVANÇADA DE OFFSETS ===\n");
    printf("[AdvancedOffsetFinder] EntityList: 0x%p\n", (void*)entityList);
    printf("[AdvancedOffsetFinder] LocalController: 0x%p\n", (void*)localController);
    printf("[AdvancedOffsetFinder] LocalPawn: 0x%p\n", (void*)localPawn);
    
    // Análise sequencial de estruturas
    bool success = true;
    
    if (!FindPawnOffsets()) {
        printf("[AdvancedOffsetFinder] ❌ Falha ao encontrar offsets do Pawn\n");
        success = false;
    }
    
    if (!FindControllerOffsets()) {
        printf("[AdvancedOffsetFinder] ❌ Falha ao encontrar offsets do Controller\n");
        success = false;
    }
    
    if (!FindPositionOffsets()) {
        printf("[AdvancedOffsetFinder] ❌ Falha ao encontrar offsets de Posição\n");
        success = false;
    }
    
    if (!FindNameOffsets()) {
        printf("[AdvancedOffsetFinder] ⚠️ Falha ao encontrar offsets de Nome (não crítico)\n");
    }
    
    if (!FindEntityOffsets()) {
        printf("[AdvancedOffsetFinder] ❌ Falha ao encontrar offsets de Entity\n");
        success = false;
    }
    
    // Validação cruzada
    if (success && CrossValidateOffsets()) {
        offsets.valid = true;
        printf("[AdvancedOffsetFinder] ✅ TODOS OS OFFSETS ENCONTRADOS E VALIDADOS!\n");
    } else {
        printf("[AdvancedOffsetFinder] ❌ Validação falhou!\n");
        offsets.valid = false;
    }
    
    printf("[AdvancedOffsetFinder] =======================================\n");
    return offsets.valid;
}

bool AdvancedOffsetFinder::FindPawnOffsets()
{
    printf("[AdvancedOffsetFinder] 🔍 Analisando estrutura do Pawn...\n");
    
    if (m_localPawn == 0) return false;
    
    // Buscar m_iHealth no pawn (mais confiável)
    uintptr_t healthOffsets[] = { 
        0x344, 0x340, 0x348, 0x33C, 0x350, 0x354, 0x338, 0x35C,
        0x360, 0x364, 0x368, 0x36C, 0x370, 0x374, 0x378, 0x37C
    };
    
    for (int i = 0; i < 16; i++) {
        if (ValidateHealthOffset(m_localPawn, healthOffsets[i])) {
            offsets.m_iHealth = healthOffsets[i];
            printf("[AdvancedOffsetFinder] ✅ m_iHealth: 0x%X\n", healthOffsets[i]);
            break;
        }
    }
    
    // Buscar m_iTeamNum no pawn
    uintptr_t teamOffsets[] = { 
        0x3E3, 0x3E0, 0x3E4, 0x3E8, 0x3DC, 0x3EC, 0x3D8, 0x3F0,
        0x3F4, 0x3F8, 0x3FC, 0x400, 0x404, 0x408, 0x40C, 0x410
    };
    
    for (int i = 0; i < 16; i++) {
        if (ValidateTeamOffset(m_localPawn, teamOffsets[i])) {
            offsets.m_iTeamNum_Pawn = teamOffsets[i];
            printf("[AdvancedOffsetFinder] ✅ m_iTeamNum (pawn): 0x%X\n", teamOffsets[i]);
            break;
        }
    }
    
    return (offsets.m_iHealth != 0 && offsets.m_iTeamNum_Pawn != 0);
}

bool AdvancedOffsetFinder::FindControllerOffsets()
{
    printf("[AdvancedOffsetFinder] 🔍 Analisando estrutura do Controller...\n");
    
    if (m_localController == 0) return false;
    
    // Buscar m_hPlayerPawn (handle para o pawn)
    uintptr_t pawnHandleOffsets[] = { 
        0x824, 0x820, 0x828, 0x82C, 0x830, 0x834, 0x838, 0x83C,
        0x7FC, 0x800, 0x804, 0x808, 0x80C, 0x810, 0x814, 0x818
    };
    
    for (int i = 0; i < 16; i++) {
        uintptr_t handle = m_memory->Read<uintptr_t>(m_localController + pawnHandleOffsets[i]);
        
        // Validar se o handle aponta para algo relacionado ao LocalPawn
        if (handle != 0 && handle != 0xFFFFFFFF) {
            // Tentar resolver o handle
            uintptr_t index = handle & 0x7FFFFFFF;
            if (index > 0 && index < 2048) {
                uintptr_t resolvedPawn = m_memory->Read<uintptr_t>(m_entityList + 0x10 + (index * 0x78));
                if (resolvedPawn == m_localPawn || (resolvedPawn > 0x10000 && resolvedPawn < 0x7FFFFFFFFFFF)) {
                    offsets.m_hPlayerPawn = pawnHandleOffsets[i];
                    printf("[AdvancedOffsetFinder] ✅ m_hPlayerPawn: 0x%X (handle: 0x%X)\n", pawnHandleOffsets[i], handle);
                    break;
                }
            }
        }
    }
    
    // Buscar m_iPawnHealth no controller
    uintptr_t controllerHealthOffsets[] = { 
        0x830, 0x834, 0x838, 0x82C, 0x828, 0x824, 0x83C, 0x840,
        0x844, 0x848, 0x84C, 0x850, 0x854, 0x858, 0x85C, 0x860
    };
    
    for (int i = 0; i < 16; i++) {
        if (ValidateHealthOffset(m_localController, controllerHealthOffsets[i])) {
            offsets.m_iPawnHealth = controllerHealthOffsets[i];
            printf("[AdvancedOffsetFinder] ✅ m_iPawnHealth (controller): 0x%X\n", controllerHealthOffsets[i]);
            break;
        }
    }
    
    // Buscar m_bPawnIsAlive
    uintptr_t aliveOffsets[] = { 
        0x82C, 0x828, 0x824, 0x830, 0x834, 0x820, 0x83C, 0x840,
        0x844, 0x848, 0x84C, 0x850, 0x854, 0x858, 0x85C, 0x860
    };
    
    for (int i = 0; i < 16; i++) {
        bool alive = m_memory->Read<bool>(m_localController + aliveOffsets[i]);
        if (alive) { // Assumindo que o jogador local está vivo
            offsets.m_bPawnIsAlive = aliveOffsets[i];
            printf("[AdvancedOffsetFinder] ✅ m_bPawnIsAlive: 0x%X\n", aliveOffsets[i]);
            break;
        }
    }
    
    return (offsets.m_hPlayerPawn != 0 || offsets.m_iPawnHealth != 0);
}

bool AdvancedOffsetFinder::FindPositionOffsets()
{
    printf("[AdvancedOffsetFinder] 🔍 Analisando offsets de posição...\n");
    
    if (m_localPawn == 0) return false;
    
    // Buscar offsets de posição mais comuns no CS2
    uintptr_t positionOffsets[] = { 
        0x1324, 0x1320, 0x1328, 0x132C, 0x1330, 0x1334, 0x1338, 0x133C,
        0x1340, 0x1344, 0x1348, 0x134C, 0x1350, 0x1354, 0x1358, 0x135C,
        0x1280, 0x1284, 0x1288, 0x128C, 0x1290, 0x1294, 0x1298, 0x129C,
        0x12A0, 0x12A4, 0x12A8, 0x12AC, 0x12B0, 0x12B4, 0x12B8, 0x12BC
    };
    
    for (int i = 0; i < 32; i++) {
        if (ValidatePositionOffset(m_localPawn, positionOffsets[i])) {
            offsets.m_vecOrigin = positionOffsets[i];
            Vector3 pos = m_memory->Read<Vector3>(m_localPawn + positionOffsets[i]);
            printf("[AdvancedOffsetFinder] ✅ m_vecOrigin: 0x%X (%.1f, %.1f, %.1f)\n", 
                   positionOffsets[i], pos.x, pos.y, pos.z);
            break;
        }
    }
    
    // Buscar m_vecAbsOrigin (alternativo)
    if (offsets.m_vecOrigin == 0) {
        uintptr_t absOriginOffsets[] = { 
            0xD0, 0xD4, 0xD8, 0xDC, 0xE0, 0xE4, 0xE8, 0xEC,
            0xF0, 0xF4, 0xF8, 0xFC, 0x100, 0x104, 0x108, 0x10C
        };
        
        for (int i = 0; i < 16; i++) {
            if (ValidatePositionOffset(m_localPawn, absOriginOffsets[i])) {
                offsets.m_vecAbsOrigin = absOriginOffsets[i];
                Vector3 pos = m_memory->Read<Vector3>(m_localPawn + absOriginOffsets[i]);
                printf("[AdvancedOffsetFinder] ✅ m_vecAbsOrigin: 0x%X (%.1f, %.1f, %.1f)\n", 
                       absOriginOffsets[i], pos.x, pos.y, pos.z);
                break;
            }
        }
    }
    
    return (offsets.m_vecOrigin != 0 || offsets.m_vecAbsOrigin != 0);
}

bool AdvancedOffsetFinder::FindNameOffsets()
{
    printf("[AdvancedOffsetFinder] 🔍 Analisando offsets de nome...\n");
    
    if (m_localController == 0) return false;
    
    // Buscar m_sSanitizedPlayerName
    uintptr_t nameOffsets[] = { 
        0x770, 0x774, 0x778, 0x77C, 0x780, 0x784, 0x788, 0x78C,
        0x790, 0x794, 0x798, 0x79C, 0x7A0, 0x7A4, 0x7A8, 0x7AC,
        0x750, 0x754, 0x758, 0x75C, 0x760, 0x764, 0x768, 0x76C
    };
    
    for (int i = 0; i < 24; i++) {
        if (ValidateNameOffset(m_localController, nameOffsets[i])) {
            offsets.m_sSanitizedPlayerName = nameOffsets[i];
            printf("[AdvancedOffsetFinder] ✅ m_sSanitizedPlayerName: 0x%X\n", nameOffsets[i]);
            return true;
        }
    }
    
    printf("[AdvancedOffsetFinder] ⚠️ Nome não encontrado (não crítico)\n");
    return false;
}

bool AdvancedOffsetFinder::FindEntityOffsets()
{
    printf("[AdvancedOffsetFinder] 🔍 Analisando estrutura da EntityList...\n");
    
    // Validar estrutura da EntityList
    offsets.m_pEntity = 0x0;  // Offset base
    offsets.m_ListEntry = 0x78; // Tamanho de cada entrada
    
    printf("[AdvancedOffsetFinder] ✅ EntityList estrutura: base=0x0, entry=0x78\n");
    return true;
}

bool AdvancedOffsetFinder::ValidateHealthOffset(uintptr_t baseAddr, uintptr_t offset)
{
    int health = m_memory->Read<int>(baseAddr + offset);
    return (health > 0 && health <= 100);
}

bool AdvancedOffsetFinder::ValidateTeamOffset(uintptr_t baseAddr, uintptr_t offset)
{
    int team = m_memory->Read<int>(baseAddr + offset);
    return (team == 2 || team == 3);
}

bool AdvancedOffsetFinder::ValidatePositionOffset(uintptr_t baseAddr, uintptr_t offset)
{
    Vector3 pos = m_memory->Read<Vector3>(baseAddr + offset);
    return IsValidPosition(pos);
}

bool AdvancedOffsetFinder::ValidateNameOffset(uintptr_t baseAddr, uintptr_t offset)
{
    char name[64] = {0};
    for (int i = 0; i < 32; i++) {
        char c = m_memory->Read<char>(baseAddr + offset + i);
        if (c == 0) break;
        name[i] = c;
    }
    return IsValidPlayerName(name);
}

bool AdvancedOffsetFinder::IsValidPlayerName(const char* name, size_t maxLen)
{
    if (!name || strlen(name) < 2 || strlen(name) > maxLen) return false;
    
    int validChars = 0;
    for (size_t i = 0; i < strlen(name); i++) {
        if ((name[i] >= 'A' && name[i] <= 'Z') || 
            (name[i] >= 'a' && name[i] <= 'z') || 
            (name[i] >= '0' && name[i] <= '9') ||
            name[i] == '_' || name[i] == '-' || name[i] == ' ') {
            validChars++;
        }
    }
    
    return (validChars >= 2 && validChars == strlen(name));
}

bool AdvancedOffsetFinder::IsValidPosition(const Vector3& pos)
{
    // Verificar se a posição está dentro de limites razoáveis para CS2
    return (abs(pos.x) < 50000 && abs(pos.y) < 50000 && abs(pos.z) < 10000 &&
            (pos.x != 0.0f || pos.y != 0.0f || pos.z != 0.0f));
}

bool AdvancedOffsetFinder::CrossValidateOffsets()
{
    printf("[AdvancedOffsetFinder] 🔍 Validação cruzada dos offsets...\n");
    
    int validOffsets = 0;
    
    if (offsets.m_iHealth != 0) validOffsets++;
    if (offsets.m_iTeamNum_Pawn != 0) validOffsets++;
    if (offsets.m_vecOrigin != 0 || offsets.m_vecAbsOrigin != 0) validOffsets++;
    if (offsets.m_hPlayerPawn != 0 || offsets.m_iPawnHealth != 0) validOffsets++;
    
    printf("[AdvancedOffsetFinder] Offsets válidos: %d/4\n", validOffsets);
    
    return (validOffsets >= 3); // Pelo menos 3 dos 4 principais offsets
}
