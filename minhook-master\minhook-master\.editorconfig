# EditorConfig is awesome: http://EditorConfig.org

# top-most EditorConfig file
root = true

# Windows-style newlines with a newline ending every file
[*]
end_of_line = crlf
insert_final_newline = true

# 4 space indentation
[*.{c,h,def}]
indent_style = space
indent_size = 4

# Trim trailing whitespaces
[*.{c,h,def,txt}]
trim_trailing_whitespace = true

# UTF-8 with BOM
[*.{c,h,def,txt}]
charset=utf-8-bom

# C/C++ code formatting
[*.{c,h}]
cpp_space_pointer_reference_alignment = right
