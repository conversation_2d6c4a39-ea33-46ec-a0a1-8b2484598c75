// Generated using https://github.com/a2x/cs2-dumper
// 2025-07-06 15:56:26.663360300 UTC

namespace CS2Dumper.Offsets {
    // Module: client.dll
    public static class ClientDll {
        public const nint dwCSGOInput = 0x1A77100;
        public const nint dwEntityList = 0x1A044C0;
        public const nint dwGameEntitySystem = 0x1B27B48;
        public const nint dwGameEntitySystem_highestEntityIndex = 0x20F0;
        public const nint dwGameRules = 0x1A68B28;
        public const nint dwGlobalVars = 0x184BEB0;
        public const nint dwGlowManager = 0x1A68278;
        public const nint dwLocalPlayerController = 0x1A52D00;
        public const nint dwLocalPlayerPawn = 0x18580D0;
        public const nint dwPlantedC4 = 0x1A71C30;
        public const nint dwPrediction = 0x1857F50;
        public const nint dwSensitivity = 0x1A69848;
        public const nint dwSensitivity_sensitivity = 0x40;
        public const nint dwViewAngles = 0x1A774D0;
        public const nint dwViewMatrix = 0x1A6D260;
        public const nint dwViewRender = 0x1A6DB70;
        public const nint dwWeaponC4 = 0x1A06550;
    }
    // Module: engine2.dll
    public static class Engine2Dll {
        public const nint dwBuildNumber = 0x540BE4;
        public const nint dwNetworkGameClient = 0x53FCE0;
        public const nint dwNetworkGameClient_clientTickCount = 0x368;
        public const nint dwNetworkGameClient_deltaTick = 0x27C;
        public const nint dwNetworkGameClient_isBackgroundMap = 0x281447;
        public const nint dwNetworkGameClient_localPlayer = 0xF0;
        public const nint dwNetworkGameClient_maxClients = 0x238;
        public const nint dwNetworkGameClient_serverTickCount = 0x36C;
        public const nint dwNetworkGameClient_signOnState = 0x228;
        public const nint dwWindowHeight = 0x62359C;
        public const nint dwWindowWidth = 0x623598;
    }
    // Module: inputsystem.dll
    public static class InputsystemDll {
        public const nint dwInputSystem = 0x387E0;
    }
    // Module: matchmaking.dll
    public static class MatchmakingDll {
        public const nint dwGameTypes = 0x1A52E0;
        public const nint dwGameTypes_mapName = 0x120;
    }
    // Module: soundsystem.dll
    public static class SoundsystemDll {
        public const nint dwSoundSystem = 0x3A15F0;
        public const nint dwSoundSystem_engineViewData = 0x7C;
    }
}
