#pragma once
#include "pch.h"
#include "offsets.h"
#include "memory.h"
#include "entity.h"
#include "render.h"
#include "menu.h"
#include <vector>

class ESP
{
private:
    Memory* m_pMemory;
    Render* m_pRender;
    Menu* m_pMenu;
    
    uintptr_t m_dwClient;
    uintptr_t m_dwEngine;
    
    // Offsets importantes
    uintptr_t m_dwEntityList;
    uintptr_t m_dwLocalPlayerController;
    uintptr_t m_dwLocalPlayerPawn;
    uintptr_t m_dwViewMatrix;
    
    // Player local
    Entity m_LocalPlayer;
    
    // Lista de entidades
    std::vector<Entity> m_Entities;
    
    // Configurações ESP
    struct ESPConfig
    {
        bool bEnabled = true;
        bool bShowBoxes = true;
        bool bShowHealth = true;
        bool bShowNames = true;
        bool bShowDistance = true;
        bool bShowTeam = false;
        float fMaxDistance = 500.0f;
        
        // Cores
        float colorEnemy[4] = { 1.0f, 0.0f, 0.0f, 1.0f }; // Vermelho
        float colorTeam[4] = { 0.0f, 1.0f, 0.0f, 1.0f };  // Verde
        float colorHealth[4] = { 0.0f, 1.0f, 0.0f, 1.0f }; // Verde
    } m_Config;
    
public:
    ESP();
    ~ESP();
    
    bool Initialize();
    void Shutdown();
    void Update();
    
private:
    bool InitializeModules();
    bool InitializeOffsets();
    void UpdateEntities();
    void UpdateLocalPlayer();
    void RenderESP();
    bool IsValidEntity(const Entity& entity);
    float GetDistance(const Vector3& pos1, const Vector3& pos2);
    void HandleInput();

public:
    // Getters para configuração (para o menu)
    const ESPConfig& GetConfig() const { return m_Config; }
    void SetConfig(const ESPConfig& config) { m_Config = config; }
};
