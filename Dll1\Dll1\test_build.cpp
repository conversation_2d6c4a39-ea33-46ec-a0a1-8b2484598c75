// Arquivo de teste para verificar se todas as dependências estão corretas
#include "pch.h"
#include "esp.h"

// Teste básico de compilação
void TestBuild()
{
    // Teste de criação de objetos
    Memory memory;
    Entity entity;
    
    // Teste de estruturas
    Vector3 pos(0, 0, 0);
    Vector2 screen(0, 0);
    
    // Teste de offsets
    uintptr_t test = Offsets::Client::dwEntityList;
    
    printf("Build test passed! Offset test: 0x%p\n", (void*)test);
}
