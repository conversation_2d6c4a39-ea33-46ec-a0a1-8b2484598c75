#pragma once
#include "pch.h"
#include "memory.h"

// Estruturas baseadas no CS2 (UnknownCheats style)
struct Vector3 {
    float x, y, z;
    Vector3() : x(0), y(0), z(0) {}
    Vector3(float x, float y, float z) : x(x), y(y), z(z) {}
};

struct PlayerInfo {
    uintptr_t controller;
    uintptr_t pawn;
    int health;
    int team;
    bool alive;
    bool valid;
    Vector3 position;
    char name[64];
    float distance;
    
    PlayerInfo() : controller(0), pawn(0), health(0), team(0), 
                   alive(false), valid(false), distance(0.0f) {
        memset(name, 0, sizeof(name));
    }
};

class EntityManager {
private:
    Memory* m_memory;
    uintptr_t m_entityList;
    uintptr_t m_localController;
    uintptr_t m_localPawn;
    
    // Offsets dinâmicos (encontrados em runtime)
    struct Offsets {
        // Controller offsets
        uintptr_t m_hPlayerPawn;
        uintptr_t m_iPawnHealth;
        uintptr_t m_bPawnIsAlive;
        uintptr_t m_iTeamNum;
        uintptr_t m_sSanitizedPlayerName;
        
        // Pawn offsets
        uintptr_t m_iHealth;
        uintptr_t m_iTeamNum_Pawn;
        uintptr_t m_vOldOrigin;
        uintptr_t m_pGameSceneNode;
        uintptr_t m_vecAbsOrigin;
        
        bool initialized;
        
        Offsets() : m_hPlayerPawn(0), m_iPawnHealth(0), m_bPawnIsAlive(0),
                   m_iTeamNum(0), m_sSanitizedPlayerName(0), m_iHealth(0),
                   m_iTeamNum_Pawn(0), m_vOldOrigin(0), m_pGameSceneNode(0),
                   m_vecAbsOrigin(0), initialized(false) {}
    } offsets;
    
    PlayerInfo m_players[64];
    int m_playerCount;
    PlayerInfo m_localPlayer;
    
public:
    EntityManager(Memory* memory);
    ~EntityManager();
    
    bool Initialize(uintptr_t entityList, uintptr_t localController, uintptr_t localPawn);
    void Update();
    
    // Getters
    const PlayerInfo* GetPlayers() const { return m_players; }
    int GetPlayerCount() const { return m_playerCount; }
    const PlayerInfo& GetLocalPlayer() const { return m_localPlayer; }
    
    // Utility
    bool IsValidPlayer(const PlayerInfo& player) const;
    float CalculateDistance(const Vector3& pos1, const Vector3& pos2) const;
    
private:
    bool FindOffsets();
    bool UpdateLocalPlayer();
    bool UpdatePlayers();
    
    uintptr_t ResolvePawnFromController(uintptr_t controller);
    bool ReadPlayerInfo(uintptr_t controller, PlayerInfo& player);
    Vector3 GetPlayerPosition(uintptr_t pawn);
    bool ReadPlayerName(uintptr_t controller, char* name, size_t maxLen);
    
    // Offset finding methods
    uintptr_t FindControllerOffset(const char* name, std::function<bool(uintptr_t, int)> validator);
    uintptr_t FindPawnOffset(const char* name, std::function<bool(uintptr_t, int)> validator);
};
