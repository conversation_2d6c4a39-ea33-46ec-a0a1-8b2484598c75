#pragma once
#include "pch.h"
#include "memory.h"
#include "advanced_offset_finder.h"
#include <functional>

// Vector3 já está definido em memory.h

struct PlayerInfo {
    uintptr_t controller;
    uintptr_t pawn;
    int health;
    int team;
    bool alive;
    bool valid;
    Vector3 position;
    char name[64];
    float distance;
    
    PlayerInfo() : controller(0), pawn(0), health(0), team(0), 
                   alive(false), valid(false), distance(0.0f) {
        memset(name, 0, sizeof(name));
    }
};

class EntityManager {
private:
    Memory* m_memory;
    uintptr_t m_entityList;
    uintptr_t m_localController;
    uintptr_t m_localPawn;
    uintptr_t m_clientBase;

    // Sistema avançado de offsets
    AdvancedOffsetFinder* m_offsetFinder;
    AdvancedOffsetFinder::DynamicOffsets m_offsets;
    
    PlayerInfo m_players[64];
    int m_playerCount;
    PlayerInfo m_localPlayer;
    
public:
    EntityManager(Memory* memory, uintptr_t clientBase);
    ~EntityManager();

    bool Initialize(uintptr_t entityList, uintptr_t localController, uintptr_t localPawn);
    bool Update();
    
    // Getters
    const PlayerInfo* GetPlayers() const { return m_players; }
    int GetPlayerCount() const { return m_playerCount; }
    const PlayerInfo& GetLocalPlayer() const { return m_localPlayer; }
    
    // Utility
    bool IsValidPlayer(const PlayerInfo& player) const;
    float CalculateDistance(const Vector3& pos1, const Vector3& pos2) const;
    
private:
    bool UpdateLocalPlayer();
    bool UpdatePlayers();

    uintptr_t ResolvePawnFromController(uintptr_t controller);
    bool ReadPlayerInfo(uintptr_t controller, PlayerInfo& player);
    Vector3 GetPlayerPosition(uintptr_t pawn);
    bool ReadPlayerName(uintptr_t controller, char* name, size_t maxLen);

    // Métodos profissionais de análise
    bool AnalyzePlayer(uintptr_t controller, PlayerInfo& player);
    bool ValidatePlayerData(const PlayerInfo& player);
    void PrintPlayerAnalysis(const PlayerInfo& player, int index);
};
